import 'dart:convert';
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart';

class EncryptionService {
  static const String _keyString = "MySecretKey12345MySecretKey12345"; // 32 bytes
  static const String _ivString = "MySecretIV123456"; // 16 bytes
  
  // إنشاء Key و IV بنفس طريقة C# (الإعدادات الافتراضية)
  static final _key = Key(utf8.encode(_keyString.substring(0, 32)));
  static final _iv = IV(utf8.encode(_ivString.substring(0, 16)));
  static final _encrypter = Encrypter(AES(_key, mode: AESMode.cbc, padding: 'PKCS7'));

  static String encrypt(String plainText) {
    if (plainText.isEmpty) return '';

    try {
      final encrypted = _encrypter.encrypt(plainText, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      print('Encryption error: $e');
      return '';
    }
  }

  static String decrypt(String cipherText) {
    if (cipherText.isEmpty) return '';

    try {
      print('Attempting to decrypt: ${cipherText.length} chars');

      // C# AES يضع IV في بداية البيانات المشفرة
      final allBytes = base64.decode(cipherText);
      print('Total bytes: ${allBytes.length}');

      if (allBytes.length <= 16) {
        print('Data too short for IV + content');
        return '';
      }

      // استخراج IV من أول 16 bytes
      final ivBytes = allBytes.sublist(0, 16);
      final encryptedBytes = allBytes.sublist(16);

      print('IV bytes: ${ivBytes.length}, Encrypted bytes: ${encryptedBytes.length}');

      final dynamicIV = IV(Uint8List.fromList(ivBytes));
      final encrypted = Encrypted(Uint8List.fromList(encryptedBytes));

      final decrypted = _encrypter.decrypt(encrypted, iv: dynamicIV);
      print('Decryption successful: ${decrypted.length} chars');
      return decrypted;

    } catch (e) {
      print('Decryption error: $e');

      // محاولة أخيرة مع IV ثابت
      try {
        print('Trying with fixed IV...');
        final encrypted = Encrypted.fromBase64(cipherText);
        final decrypted = _encrypter.decrypt(encrypted, iv: _iv);
        print('Fixed IV successful');
        return decrypted;
      } catch (e2) {
        print('Fixed IV also failed: $e2');
        return '';
      }
    }
  }

  static String encryptObject<T>(T object) {
    final json = jsonEncode(object);
    return encrypt(json);
  }

  static T? decryptObject<T>(String encryptedData, T Function(Map<String, dynamic>) fromJson) {
    try {
      final decryptedJson = decrypt(encryptedData);
      if (decryptedJson.isEmpty) return null;
      
      final jsonMap = jsonDecode(decryptedJson) as Map<String, dynamic>;
      return fromJson(jsonMap);
    } catch (e) {
      print('Decrypt object error: $e');
      return null;
    }
  }

  static Map<String, dynamic>? decryptToMap(String encryptedData) {
    try {
      final decryptedJson = decrypt(encryptedData);
      if (decryptedJson.isEmpty) return null;
      
      return jsonDecode(decryptedJson) as Map<String, dynamic>;
    } catch (e) {
      print('Decrypt to map error: $e');
      return null;
    }
  }

  static List<T>? decryptToList<T>(String encryptedData, T Function(Map<String, dynamic>) fromJson) {
    try {
      final decryptedJson = decrypt(encryptedData);
      if (decryptedJson.isEmpty) return null;
      
      final jsonList = jsonDecode(decryptedJson) as List;
      return jsonList.map((item) => fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
      print('Decrypt to list error: $e');
      return null;
    }
  }
}
