import 'dart:io';
import 'dart:typed_data';
import 'package:elecollect_app/models/invoice_receipt.dart';
import 'package:flutter/foundation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:flutter/services.dart';
import '../models/collection.dart';
import '../models/user.dart';

class PrintService {
  static pw.Font? _arabicFont;
  static pw.Font? _arabicBoldFont;
  static pw.Font? _fallbackFont;
  static pw.Font? _fallbackBoldFont;

  // Logo variable
  static pw.ImageProvider? _logoImage;

  // Load Arabic fonts with fallback support
  static Future<void> _loadArabicFonts() async {
    if (_arabicFont == null || _arabicBoldFont == null) {
      try {
        // Load Google Fonts with Arabic support and fallback fonts
        final arabicRegular = await PdfGoogleFonts.notoSansArabicRegular();
        final arabicBold = await PdfGoogleFonts.notoSansArabicBold();
        final fallbackRegular = await PdfGoogleFonts.notoSansRegular();
        final fallbackBold = await PdfGoogleFonts.notoSansBold();

        _arabicFont = arabicRegular;
        _arabicBoldFont = arabicBold;
        _fallbackFont = fallbackRegular;
        _fallbackBoldFont = fallbackBold;
      } catch (e) {
        // Fallback to basic fonts
        _arabicFont = await PdfGoogleFonts.notoSansRegular();
        _arabicBoldFont = await PdfGoogleFonts.notoSansBold();
      }
    }

    // Load logo if not already loaded
    if (_logoImage == null) {
      try {
        final logoData = await rootBundle.load('assets/images/peclogo.png');
        _logoImage = pw.MemoryImage(logoData.buffer.asUint8List());
      } catch (e) {
        // Logo loading failed, continue without logo
        _logoImage = null;
      }
    }
  }

  static Future<void> printCollectionReceipt({
      required  InvoiceReceipt invoiceReceipt
   // required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
   // required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) async {
    try {
      // Load Arabic fonts first
      await _loadArabicFonts();


   // Check if running on mobile
    /*  if (defaultTargetPlatform == TargetPlatform.android ||
          defaultTargetPlatform == TargetPlatform.iOS) {
        // Use mobile-friendly printing
        await _printForMobile(
          subscriber: subscriber,
          collectionResponse: collectionResponse,
          amountPaid: amountPaid,
          paymentMethod: paymentMethod,
          notes: notes,
        );
      } else {
        // Use desktop printing
        await _printDirectToThermalPrinter(
          subscriber: subscriber,
          collectionResponse: collectionResponse,
          amountPaid: amountPaid,
          paymentMethod: paymentMethod,
          notes: notes,
        );
      }*/


      // Try direct printing without queue first
      await _printDirectToThermalPrinter(
invoiceReceipt: invoiceReceipt
      //  subscriber: subscriber,
      //  collectionResponse: collectionResponse,
       // amountPaid: amountPaid,
       // paymentMethod: paymentMethod,
       // notes: notes,
      );
    } catch (e) {
      print(e);
      // Fallback to enhanced printing if direct fails
      try {
        await _printEnhancedThermalReceipt(
          invoiceReceipt: invoiceReceipt,
         // subscriber: subscriber,
         // collectionResponse: collectionResponse,
         // amountPaid: amountPaid,
         // paymentMethod: paymentMethod,
         // notes: notes,
        );
      } catch (fallbackError) {
        // Final fallback
        await _printWithFallback(
          invoiceReceipt
         // subscriber, collectionResponse, amountPaid, paymentMethod, notes
          );
      }
    }
  }


  // Mobile-friendly printing
  static Future<void> _printForMobile({
        required  InvoiceReceipt invoiceReceipt
    //required Subscriber subscriber,
    //required CollectionResponse collectionResponse,
    //required double amountPaid,
    //required String paymentMethod,
   // String? notes,
  }) async {
    final pdf = pw.Document();

    // Create mobile-friendly format
    const mobileFormat = PdfPageFormat(
      75 * PdfPageFormat.mm, // 72mm width
      double.infinity, // Auto height
      marginAll: 1 * PdfPageFormat.mm, // Safe margins
    );

    // Create receipt content
    pdf.addPage(
      pw.Page(
        pageFormat: mobileFormat,
        margin: const pw.EdgeInsets.all(4),
        build: (pw.Context context) {
          return _buildUltraCompactReceipt(
            invoiceReceipt: invoiceReceipt,
           // subscriber: subscriber,
           // collectionResponse: collectionResponse,
           // amountPaid: amountPaid,
           // paymentMethod: paymentMethod,
           // notes: notes,
          );
        },
      ),
    );

    try {
      // Try to use system print dialog
      await Printing.layoutPdf(
        onLayout: (format) async => pdf.save(),
        name: 'سند_تحصيل_${invoiceReceipt.subscriberNumber}',
        format: mobileFormat,
      );
    } catch (e) {
      // If layoutPdf fails, try sharing the PDF
      final pdfBytes = await pdf.save();
      await _sharePdfFile(pdfBytes, 'سند_تحصيل_${invoiceReceipt.subscriberNumber}.pdf');
    }
  }

 // Share PDF file as fallback for mobile
  static Future<void> _sharePdfFile(Uint8List pdfBytes, String fileName) async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // Create a temporary file
        final tempDir = await Directory.systemTemp.createTemp();
        final file = File('${tempDir.path}/$fileName');
        await file.writeAsBytes(pdfBytes);

        // Share the file
        // Note: This would require share_plus package
        // For now, we'll just show the PDF in the system viewer
        await Printing.sharePdf(bytes: pdfBytes, filename: fileName);
      }
    } catch (e) {
      throw Exception('فشل في مشاركة الملف: ${e.toString()}');
    }
  }
  // Direct printing to thermal printer without queue
  static Future<void> _printDirectToThermalPrinter({
        required  InvoiceReceipt invoiceReceipt
  //  required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
  //  required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) async {
    final pdf = pw.Document();

    // Create minimal format for direct printing
    const directFormat = PdfPageFormat(
      75 * PdfPageFormat.mm, // 72mm width (optimized for safe printing)
      120 * PdfPageFormat.mm, // Fixed height for 80mm paper
      marginAll: 1 * PdfPageFormat.mm, // Increased margins for 72mm width
    );

    // Create ultra-minimal receipt content
    pdf.addPage(
      pw.Page(
        pageFormat: directFormat,
        margin: const pw.EdgeInsets.all(10), // Increased margin for 72mm width
        build: (pw.Context context) {
          return _buildMinimalReceipt(
invoiceReceipt: invoiceReceipt,
        //    subscriber: subscriber,
        //    collectionResponse: collectionResponse,
        //    amountPaid: amountPaid,
        //    paymentMethod: paymentMethod,
        //    notes: notes,
          );
        },
      ),
    );

    // Print directly without layout dialog
    final printer = await _findBestThermalPrinter();

    try {
      // Try direct printing first
      await Printing.directPrintPdf(
        printer: printer,
        onLayout: (format) async => pdf.save(),
        name: 'Receipt_${invoiceReceipt.subscriberNumber}',
        format: directFormat,
        usePrinterSettings: false, // Don't use printer queue settings
      );
    } catch (e) {

       print(e);
      // If direct printing fails, try alternative method
      await _printWithoutQueue(pdf, printer, 'Receipt_${invoiceReceipt.subscriberNumber}');
    }
  }

  // Find the best thermal printer for direct printing
  static Future<Printer> _findBestThermalPrinter() async {
    final printers = await Printing.listPrinters();

    // Look for thermal printer by name patterns (prioritized)
    for (final printer in printers) {
      final name = printer.name.toLowerCase();
        print(name);
     /* if (name.contains('mht-p29l') || name.contains('mht') || name.contains('rpp300')) {
        return printer;
      }*/
       if ( name.contains('rongta')) {
        return printer;
      }

    }

    // Look for other thermal printers (prioritize 80mm)
    for (final printer in printers) {
      final name = printer.name.toLowerCase();

      print(name);
      if (name.contains('rpp300') || name.contains('rongta') ||name.contains('80mm') ||
          name.contains('thermal') ||
          name.contains('pos') ||
          name.contains('receipt') ||
          name.contains('58mm')) {
        return printer;
      }
    }

    // Return default printer if no thermal printer found
    if (printers.isNotEmpty) {
        print(printers.first.name);
      return printers.first;
    }

    throw Exception('لم يتم العثور على أي طابعة');
  }

  // Alternative printing method without queue
  static Future<void> _printWithoutQueue(pw.Document pdf, Printer printer, String name) async {
    try {
      // Try direct printing with specific printer
      await Printing.directPrintPdf(
        printer: printer,
        onLayout: (format) async => pdf.save(),
        name: name,
        format: const PdfPageFormat(
          75 * PdfPageFormat.mm,
          120 * PdfPageFormat.mm,
          marginAll: 1 * PdfPageFormat.mm,
        ),
        usePrinterSettings: true, // Use printer's own settings
      );
    } catch (e) {
      // Final attempt with layout PDF (user will select printer)
      await Printing.layoutPdf(
        onLayout: (format) async => pdf.save(),
        name: name,
        format: const PdfPageFormat(
          75 * PdfPageFormat.mm,
          120 * PdfPageFormat.mm,
          marginAll: 1 * PdfPageFormat.mm,
        ),
      );
    }
  }

  // Enhanced thermal printing with proper Arabic font support
  static Future<void> _printEnhancedThermalReceipt({
     required  InvoiceReceipt invoiceReceipt
   // required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
   // required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) async {
    final pdf = pw.Document();

    // Create ultra-compact format for thermal printer (72mm width)
    const thermalFormat = PdfPageFormat(
      75 * PdfPageFormat.mm, // 72mm width for thermal printers (optimized for safe printing)
      double.infinity, // Auto height
      marginAll: 1 * PdfPageFormat.mm, // Increased margins for 72mm width
    );

    // Create receipt content with proper Arabic font support
    pdf.addPage(
      pw.Page(
        pageFormat: thermalFormat,
        margin: const pw.EdgeInsets.all(4), // Increased margin for 72mm width
        build: (pw.Context context) {
          return _buildUltraCompactReceipt(
            invoiceReceipt: invoiceReceipt,
            //subscriber: subscriber,
           // collectionResponse: collectionResponse,
           // amountPaid: amountPaid,
           // paymentMethod: paymentMethod,
           // notes: notes,
          );
        },
      ),
    );

    // Print with thermal format
    await Printing.layoutPdf(
      onLayout: (format) async => pdf.save(),
      name: 'سند_تحصيل_${invoiceReceipt.subscriberNumber}',
      format: thermalFormat,
    );
  }

  // Build ultra-compact receipt for thermal printers
  static pw.Widget _buildUltraCompactReceipt({
         required  InvoiceReceipt invoiceReceipt
   // required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
   // required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        // Logo at the top
        if (_logoImage != null)
          pw.Container(
            height: 30, // Logo height
            child: pw.Image(_logoImage!),
          ),
        if (_logoImage != null) pw.SizedBox(height: 3),

        // Header for 72mm paper (optimized for safe printing)
        pw.Text(
          'سند تحصيل',
          style: _createTextStyleWithFallback(fontSize: 14, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 2),
        pw.Text(
          'المؤسسة العامة للكهرباء',
          style: _createTextStyleWithFallback(fontSize: 10, isBold: false),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 4),

        // Transaction details for 72mm paper (optimized for safe printing)
        _buildUltraCompactRow('رقم السند:', '${invoiceReceipt.docId ?? 'غير محدد'}'),
        _buildUltraCompactRow('تاريخ التحصيل:', _formatDateTime(invoiceReceipt.dateInvoice  )),
            _buildUltraCompactRow('تاريخ الطباعة:', _formatDateTime(invoiceReceipt.datePrint  )),
      //  _buildUltraCompactRow('الوقت:', _formatTime(collectionResponse.transactionDate ?? DateTime.now())),

        pw.Container(height: 1, color: PdfColors.black),
        pw.SizedBox(height: 2),

        // Subscriber details for 72mm paper
        _buildUltraCompactRow('رقم الاشتراك:', invoiceReceipt.subscriberNumber!),
        _buildUltraCompactRow('اسم المشترك:', invoiceReceipt.subscriberName!),
        if (invoiceReceipt.meterNumber != null && invoiceReceipt.meterNumber!.isNotEmpty)
          _buildUltraCompactRow('رقم العداد:', invoiceReceipt.meterNumber!),

        pw.Container(height: 1, color: PdfColors.black),
        pw.SizedBox(height: 2),

        // Payment details for 72mm paper
        _buildUltraCompactRow('المبلغ المستحق:', '${invoiceReceipt.dueAmount!.toStringAsFixed(2)} ر.ي'),
        _buildUltraCompactRow('المبلغ المدفوع:', '${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي', isBold: true),
        _buildUltraCompactRow('طريقة الدفع:', _getPaymentMethodText(invoiceReceipt.paymentType)),

       // if (invoiceReceipt.newBalance != null)
       //   _buildUltraCompactRow('الرصيد الجديد:', '${invoiceReceipt.newBalance!.toStringAsFixed(2)} ر.ي'),
 _buildUltraCompactRow('اسم المحصل:', invoiceReceipt.collector!),
        if (invoiceReceipt.notes != null && invoiceReceipt.notes!.isNotEmpty)
          _buildUltraCompactRow('ملاحظات:', invoiceReceipt.notes!),

        pw.SizedBox(height: 4),
        pw.Text(
          'شكراً لكم',
          style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 2),
      ],
    );
  }



  // Fallback printing method
  static Future<void> _printWithFallback(
    InvoiceReceipt invoiceReceipt
  //  Subscriber subscriber,
  //  CollectionResponse collectionResponse,
  //  double amountPaid,
  //  String paymentMethod,
  //  String? notes,
  ) async {
    final pdf = pw.Document();

    // Use minimal page format
    const minimalFormat = PdfPageFormat(
      58 * PdfPageFormat.mm,
      100 * PdfPageFormat.mm, // Fixed small height
      marginAll: 1 * PdfPageFormat.mm,
    );

    pdf.addPage(
      pw.Page(
        pageFormat: minimalFormat,
        margin: const pw.EdgeInsets.all(1),
        build: (context) => _buildThermalReceiptContent(
 invoiceReceipt: invoiceReceipt
        //  subscriber: subscriber,
        //  collectionResponse: collectionResponse,
        //  amountPaid: amountPaid,
       //  paymentMethod: paymentMethod,
        //  notes: notes,
        ),
      ),
    );

    await Printing.layoutPdf(
      onLayout: (format) async => pdf.save(),
      name: 'سند_تحصيل_${invoiceReceipt.subscriberNumber}',
      format: minimalFormat,
    );
  }

  // Optimized content for thermal printers
  static pw.Widget _buildThermalReceiptContent({
      required  InvoiceReceipt invoiceReceipt
   // required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
   // required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        // Compact Header
        pw.Text(
          'سند تحصيل',
          style: _createTextStyle(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
        pw.Text(
          'المؤسسة العامة للكهرباء',
          style: _createTextStyle(fontSize: 8, isBold: false),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 2),

        // Compact transaction details
        _buildCompactRow('رقم السند:', '${invoiceReceipt.docId ?? 'غير محدد'}'),
        _buildCompactRow('تاريخ التحصيل:', _formatDateTime(invoiceReceipt.dateInvoice  )),
        _buildCompactRow('تاريخ الطباعة:', _formatDateTime(invoiceReceipt.datePrint  )),

        pw.Container(height: 1, color: PdfColors.black),

        // Compact subscriber details
        _buildCompactRow('رقم الاشتراك:', invoiceReceipt.subscriberNumber!),
        _buildCompactRow('اسم المشترك:', invoiceReceipt.subscriberName!),
        if (invoiceReceipt.meterNumber != null && invoiceReceipt.meterNumber!.isNotEmpty)
          _buildCompactRow('رقم العداد:', invoiceReceipt.meterNumber!),

        pw.Container(height: 1, color: PdfColors.black),

        // Compact payment details
        _buildCompactRow('المبلغ المستحق:', '${invoiceReceipt.dueAmount!.toStringAsFixed(2)} ر.ي'),
        _buildCompactRow('المبلغ المدفوع:', '${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي', isBold: true),
        _buildCompactRow('طريقة الدفع:', _getPaymentMethodText(invoiceReceipt.paymentType)),

       // if (invoiceReceipt.newBalance != null)
       //   _buildCompactRow('الرصيد الجديد:', '${invoiceReceipt.newBalance!.toStringAsFixed(2)} ر.ي'),
     _buildCompactRow('اسم المحصل:', invoiceReceipt.collector!),
        pw.SizedBox(height: 2),
        pw.Text(
          'شكراً لكم',
          style: _createTextStyle(fontSize: 8, isBold: false),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 2),
      ],
    );
  }

  static pw.Widget _buildReceiptContent({
       required  InvoiceReceipt invoiceReceipt
   // required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
   // required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        // Header
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(8),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(width: 2),
          ),
          child: pw.Column(
            children: [
              pw.Text(
                'سند تحصيل',
                style: _createTextStyle(fontSize: 16, isBold: true), // تقليل حجم الخط
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(height: 2), // تقليل المسافة
              pw.Text(
                'شركة الكهرباء',
                style: _createTextStyle(fontSize: 12, isBold: false), // تقليل حجم الخط
                textAlign: pw.TextAlign.center,
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
        ),
        
        pw.SizedBox(height: 4), // تقليل المسافة
        
        // Transaction details
        _buildMixedContentRow('رقم السند:', '${invoiceReceipt.docId ?? 'غير محدد'}'),
        _buildMixedContentRow('تاريخ التحصيل:', _formatDateTime(invoiceReceipt.dateInvoice )),
          _buildMixedContentRow('تاريخ الطباعة:', _formatDateTime(invoiceReceipt.datePrint )),
     //   _buildMixedContentRow('الوقت:', _formatTime(collectionResponse.transactionDate ?? DateTime.now())),

        pw.Divider(thickness: 0.5), // تقليل سماكة الخط

        // Subscriber details
        _buildMixedContentRow('رقم الاشتراك:', invoiceReceipt.subscriberNumber!),
        _buildMixedContentRow('اسم المشترك:', invoiceReceipt.subscriberName!),
        
        if (invoiceReceipt.meterNumber != null && invoiceReceipt.meterNumber!.isNotEmpty)
          _buildMixedContentRow('رقم العداد:', invoiceReceipt.meterNumber!),

        pw.Divider(thickness: 0.5), // تقليل سماكة الخط

        // Payment details
        _buildMixedContentRow('المبلغ المستحق:', '${invoiceReceipt.dueAmount!.toStringAsFixed(2)} ريال'),
        _buildMixedContentRow('المبلغ المدفوع:', '${invoiceReceipt.paidAmount.toStringAsFixed(2)} ريال', isBold: true),
        _buildMixedContentRow('طريقة الدفع:', _getPaymentMethodText(invoiceReceipt.paymentType)),

      //  if (invoiceReceipt.newBalance != null)
        //  _buildMixedContentRow('الرصيد الجديد:', '${invoiceReceipt.newBalance!.toStringAsFixed(2)} ريال'),


  _buildMixedContentRow('اسم المحصل:', invoiceReceipt.collector!),
        if (invoiceReceipt.notes != null && invoiceReceipt.notes!.isNotEmpty)
          _buildMixedContentRow('ملاحظات:', invoiceReceipt.notes!),
        
        pw.Divider(thickness: 1), // تقليل سماكة الخط الختامي
        
        // Footer
        pw.SizedBox(height: 4), // تقليل المسافة
        pw.Text(
          'شكراً لكم',
          style: _createTextStyle(fontSize: 12, isBold: true), // تقليل حجم الخط
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),

        pw.SizedBox(height: 2), // تقليل المسافة
        pw.Text(
          'تم الطباعة: ${_formatDateTime(DateTime.now())}',
          style: _createTextStyle(fontSize: 10, isBold: false),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),

        pw.SizedBox(height: 4), // تقليل المسافة قبل الباركود
        
        // Barcode (optional)
        if (invoiceReceipt.docId != null)
          pw.BarcodeWidget(
            barcode: pw.Barcode.code128(),
            data: '${invoiceReceipt.docId}',
            width: 100, // تقليل العرض
            height: 20, // تقليل الارتفاع
          ),
      ],
    );
  }



  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')}';
  }

  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _getPaymentMethodText(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'نقدي';
      case 'card':
        return 'بطاقة';
      case 'transfer':
        return 'تحويل';
      default:
        return method;
    }
  }

  // Compact row for thermal printers
  static pw.Widget _buildCompactRow(String label, String value, {bool isBold = false}) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 1),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              _fixArabicText(value),
              style: _createTextStyle(fontSize: 8, isBold: isBold),
              textAlign: pw.TextAlign.left,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              _fixArabicText(label),
              style: _createTextStyle(fontSize: 8, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  // Build minimal receipt for direct printing
  static pw.Widget _buildMinimalReceipt({
        required  InvoiceReceipt invoiceReceipt
   // required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
   // required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        // Logo at the top
        if (_logoImage != null)
          pw.Container(
            height: 50, // Smaller logo for minimal receipt
            child: pw.Image(_logoImage!),
          ),
        if (_logoImage != null) pw.SizedBox(height: 2),

        // Minimal Header for 72mm (optimized for safe printing)
        pw.Text(
          'سند تحصيل',
          style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
        pw.Text(
          'المؤسسة العامة للكهرباء',
          style: _createTextStyleWithFallback(fontSize: 8, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
        pw.SizedBox(height: 1),

        // Essential details only
        _buildMinimalRow('رقم المشترك:', invoiceReceipt.subscriberNumber!),
        _buildMinimalRow('اسم المشترك:', invoiceReceipt.subscriberName!),
        _buildMinimalRow('المبلغ المدفوع:', '${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي', isBold: true),
        _buildMinimalRow('طريقة الدفع:', _getPaymentMethodText(invoiceReceipt.paymentType)),
        _buildMinimalRow('تاريخ التحصيل:', _formatDateTime(invoiceReceipt.dateInvoice  )),

        pw.SizedBox(height: 2),
        pw.Text(
          'شكراً لكم',
          style: _createTextStyleWithFallback(fontSize: 8, isBold: false),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
        ),
      ],
    );
  }

  // Minimal row for direct printing (72mm - optimized for safe printing)
  static pw.Widget _buildMinimalRow(String label, String value, {bool isBold = false}) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 1),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              value,
              style: _createTextStyleWithFallback(fontSize: 8, isBold: isBold),
              textAlign: pw.TextAlign.left,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              label,
              style: _createTextStyleWithFallback(fontSize: 8, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  // Ultra-compact row for 72mm thermal printers (optimized for safe printing)
  static pw.Widget _buildUltraCompactRow(String label, String value, {bool isBold = false}) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 1),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              value,
              style: _createTextStyleWithFallback(fontSize: 9, isBold: isBold),
              textAlign: pw.TextAlign.left,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              label,
              style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  // Create TextStyle with comprehensive font fallback for Arabic
  static pw.TextStyle _createTextStyleWithFallback({
    required double fontSize,
    required bool isBold,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
      font: isBold ? _arabicBoldFont : _arabicFont,
      fontFallback: [
        // Arabic fonts
        if (_arabicBoldFont != null && _arabicFont != null) ...[
          isBold ? _arabicBoldFont! : _arabicFont!,
        ],
        // Fallback fonts
        if (_fallbackBoldFont != null && _fallbackFont != null) ...[
          isBold ? _fallbackBoldFont! : _fallbackFont!,
        ],
      ],
    );
  }

  // Check if text contains Arabic characters
  static bool _isArabicText(String text) {
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return arabicRegex.hasMatch(text);
  }

  // Create TextStyle with proper font fallback
  static pw.TextStyle _createTextStyle({
    required double fontSize,
    required bool isBold,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
      font: isBold ? _arabicBoldFont : _arabicFont,
      fontFallback: [
        if (_fallbackBoldFont != null && _fallbackFont != null) ...[
          isBold ? _fallbackBoldFont! : _fallbackFont!,
        ]
      ],
    );
  }

  // Fix Arabic text direction and character ordering
  static String _fixArabicText(String text) {
    if (!_isArabicText(text)) {
      return text; // Return as-is if not Arabic
    }

    String trimmedText = text.trim();
    if (trimmedText.isEmpty) return text;

    // For Arabic text that appears reversed, we need to fix it
    // The issue is that the text might be coming from the database already reversed

    // Split into words
    List<String> words = trimmedText.split(' ');

    // Check if the text looks reversed by examining the first word
    if (words.isNotEmpty && _isArabicText(words.first)) {
      // Try reversing each word and the word order
      List<String> fixedWords = words.map((word) {
        if (_isArabicText(word) && word.length > 1) {
          return word;
        }
        return word;
      }).toList();

      // Reverse the order of words for Arabic text
      String result = fixedWords.reversed.join(' ');
      return result;
    }

    return text;
  }



  // Create a specialized row for mixed content (Arabic labels with English/Arabic values)
  static pw.Widget _buildMixedContentRow(String arabicLabel, String value, {bool isBold = false}) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 1.5), // تقليل المسافة العمودية
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // Value on the left (for numbers/English)
          pw.Expanded(
            flex: 2,
            child: pw.Container(
              alignment: _isArabicText(value) ? pw.Alignment.centerRight : pw.Alignment.centerLeft,
              child: pw.Text(
                _fixArabicText(value),
                style: _createTextStyle(
                  fontSize: isBold ? 12 : 10,
                  isBold: isBold,
                ),
                textAlign: _isArabicText(value) ? pw.TextAlign.right : pw.TextAlign.left,
                textDirection: _isArabicText(value) ? pw.TextDirection.rtl : pw.TextDirection.ltr,
              ),
            ),
          ),
          // Separator
          pw.SizedBox(width: 8),
          // Arabic label on the right
          pw.Expanded(
            flex: 2,
            child: pw.Container(
              alignment: pw.Alignment.centerRight,
              child: pw.Text(
                arabicLabel,
                style: _createTextStyle(fontSize: 10, isBold: true),
                textAlign: pw.TextAlign.right,
                textDirection: pw.TextDirection.rtl,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Preview receipt before printing
  static Future<Uint8List> generateReceiptPdf({
        required  InvoiceReceipt invoiceReceipt
    //required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
   // required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) async {
    // Load Arabic fonts first
    await _loadArabicFonts();

    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.roll80,
        margin: const pw.EdgeInsets.all(8),
        build: (pw.Context context) {
          return _buildReceiptContent(
            invoiceReceipt: invoiceReceipt,
          //  subscriber: subscriber,
          //  collectionResponse: collectionResponse,
          //  amountPaid: amountPaid,
          //  paymentMethod: paymentMethod,
          //  notes: notes,
          );
        },
      ),
    );

    return await pdf.save();
  }
}
