import 'dart:io';

import 'package:elecollect_app/theme/app_theme.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../services/storage_service.dart';
import '../models/user.dart';
import '../theme/app_colors.dart';
import '../theme/app_styles.dart';
import '../theme/app_icons.dart';
import 'subscribers_screen.dart';
import 'collection_screen.dart';
import 'meter_reading_screen.dart';
import 'collection_reports_screen.dart';
import 'login_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    final user = await StorageService.getUser();
    setState(() {
      _currentUser = user;
      _isLoading = false;
    });
  }

  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await StorageService.clearAll();
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => Directionality( textDirection: TextDirection.rtl,  child: const LoginScreen())),
          (route) => false,
        );
      }
    }
  }

  void _shareApp() {
    Share.share(
      'تطبيق EleCollect - تطبيق تحصيل الكهرباء\nتطبيق متخصص لمندوبي مؤسسة الكهرباء',
      subject: 'تطبيق EleCollect',
    );
  }
late int columnsCount=2;

  @override
  Widget build(BuildContext context) {
    columnsCount = kIsWeb || Platform.isWindows ?
                     MediaQuery.of(context).size.width >800 ? 4 :
                        MediaQuery.of(context).size.width >400 ?  2 : 1 
                  : 2;

      

    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'PEC',
          style: AppStyles.textOnDark.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor:   AppTheme.themeData.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textOnDark),
      ),
      drawer: Drawer(
        child: Column(
          children: [
            // رأس القائمة الجانبية
            Row(
                           crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Container(
                    height: 300,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [  AppTheme.themeData.primaryColor,   AppTheme.themeData.primaryColorLight],
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // شعار المؤسسة
                             Padding(
                padding: const EdgeInsets.only(top: 2.0),
                child: Image(
                    height:   90,
                    width:   90,
                    fit: BoxFit.fill,
                    image: const AssetImage('assets/images/peclogo.png')),
              ),
                 const SizedBox(height: 15),
                  Text(
                              'المؤسسة العامة للكهرباء',
                             style: AppStyles.textOnDark.copyWith(
            fontWeight: FontWeight.bold,
          ),
                            ),
                             const SizedBox(height: 32),
                          Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
                          children: [
                        
                                 Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                             color: AppColors.cardBackground,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child:   Icon(
                            Icons.person, 
                            //color: AppColors.textOnDark,
                             color:   AppTheme.themeData.primaryColor,
                            size: 40,

                            
                          ),
                        ),
                         const SizedBox(width: 8),
                             Text(
                          _currentUser?.fullName ?? 'مستخدم',
                          style: AppStyles.textOnDark.copyWith(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _currentUser?.username ?? '',
                          style: AppStyles.textOnDark.copyWith(
                            fontSize: 14,
                            color: AppColors.withOpacity(AppColors.textOnDark, 7),
                          ),
                        ),

                         
                          ],
                        ),
                       
                        // Logo
                  
                     
                        //const SizedBox(height: 16),
                        // اسم المستخدم
                    
                      ],
                    ),
                  ),
                ),
              ],
            ),
            
            // عناصر القائمة
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  ListTile(
                    leading: const Icon(Icons.share),
                    title: const Text('مشاركة التطبيق'),
                    onTap: _shareApp,
                  ),
                  const Divider(),
                  ListTile(
                    leading: Icon(Icons.logout, color: AppColors.error),
                    title: Text(
                      'تسجيل الخروج',
                      style: TextStyle(color: AppColors.error),
                    ),
                    onTap: _logout,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [  AppTheme.themeData.primaryColor,   AppTheme.themeData.primaryColorLight],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // ترحيب
                Text(
                  'مرحباً، ${_currentUser?.fullName ?? 'مستخدم'}',
                  style: AppStyles.headingMedium.copyWith(
                    color: AppColors.textOnDark,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'اختر العملية التي تريد تنفيذها',
                  style: AppStyles.bodyLarge.copyWith(
                    color: AppColors.withOpacity(AppColors.textOnDark, 7),
                  ),
                ),
                
                const SizedBox(height: 40),
                
                // الإجراءات الرئيسية
                Expanded(
                  child: GridView.count(
                    crossAxisCount:  columnsCount,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: [
                      // عرض المشتركين
                      if (_currentUser?.hasPermission('view_subscribers') ?? false)
                        _buildActionCard(
                          icon: Icons.people,
                          title: 'عرض المشتركين',
                          subtitle: 'عرض وإدارة بيانات المشتركين',
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => Directionality( textDirection: TextDirection.rtl, child: const SubscribersScreen()),
                              ),
                            );
                          },
                        ),
                      
                      // تحصيل الفواتير
                      if (_currentUser?.hasPermission('collection') ?? false)
                        _buildActionCard(
                          icon: Icons.payment,
                          title: 'تحصيل الفواتير',
                          subtitle: 'تحصيل المبالغ المستحقة',
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => Directionality( textDirection: TextDirection.rtl,
                                  child: const SubscribersScreen(
                                    actionType: SubscriberActionType.collection,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      
                      // قراءة العدادات
                      if (_currentUser?.hasPermission('meter_reading') ?? false)
                        _buildActionCard(
                          icon: Icons.qr_code_scanner,
                          title: 'قراءة العدادات',
                          subtitle: 'قراءة وتسجيل بيانات العدادات',
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => Directionality( textDirection: TextDirection.rtl, child: const MeterReadingScreen()),
                              ),
                            );
                          },
                        ),

                      // تقارير التحصيلات
                   if (_currentUser?.hasPermission('reports') ?? false)
                        _buildActionCard(
                          icon: Icons.assessment,
                          title: 'تقارير التحصيلات',
                          subtitle: 'عرض وتصدير تقارير التحصيلات',
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => Directionality(
                                  textDirection: TextDirection.rtl,
                                  child: const CollectionReportsScreen()
                                ),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color:   AppTheme.themeData.primaryColor,
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: AppStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color:   AppTheme.themeData.primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
