import 'user.dart';

class CollectionRequest {
  final String subscriberNumber;
  final double dueAmount;
  final double paidAmount;
  final String paymentType;
  final String processDate;
  final String currency;
  final String transactionId;

  CollectionRequest({
    required this.subscriberNumber,
    required this.dueAmount,
    required this.paidAmount,
    this.paymentType = 'cash',
    required this.processDate,
    this.currency = 'ry',
    required this.transactionId,
  });

  Map<String, dynamic> toJson() {
    return {
      'Payment_Type': paymentType,
      'Billing_No': subscriberNumber,
      'DueAmt': dueAmount.toString(),
      'PaidAmt': paidAmount.toString(),
      'Process_Date': processDate,
      'Currency': currency,
      'transid': transactionId,
    };
  }
}

class CollectionResponse {
  final bool success;
  final String message;
  final int? transactionId;
  final double? newBalance;
  final DateTime? transactionDate;

  CollectionResponse({
    required this.success,
    required this.message,
    this.transactionId,
    this.newBalance,
    this.transactionDate,
  });

  factory CollectionResponse.fromJson(Map<String, dynamic> json) {
    return CollectionResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      transactionId: json['transactionId'],
      newBalance: json['newBalance']?.toDouble(),
      transactionDate: json['transactionDate'] != null
          ? DateTime.parse(json['transactionDate'])
          : null,
    );
  }
}

class BalanceInquiryRequest {
  final String subscriberNumber;

  BalanceInquiryRequest({
    required this.subscriberNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'Billing_No': subscriberNumber,
    };
  }
}

class BalanceInquiryResponse {
  final bool success;
  final String message;
  final double currentBalance;
  final double lastReading;
  final DateTime? lastReadingDate;
  final double estimatedBill;
  final Subscriber? subscriber;
  final String? subscriberName;
  final String? billNumber;
  final String? billStatus;
  final String? issueDate;
  final String? dueDate;

  BalanceInquiryResponse({
    required this.success,
    required this.message,
    required this.currentBalance,
    required this.lastReading,
    this.lastReadingDate,
    required this.estimatedBill,
    this.subscriber,
    this.subscriberName,
    this.billNumber,
    this.billStatus,
    this.issueDate,
    this.dueDate,
  });

  factory BalanceInquiryResponse.fromJson(Map<String, dynamic> json) {
    return BalanceInquiryResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      currentBalance: (json['currentBalance'] ?? 0.0).toDouble(),
      lastReading: (json['lastReading'] ?? 0.0).toDouble(),
      lastReadingDate: json['lastReadingDate'] != null
          ? DateTime.parse(json['lastReadingDate'])
          : null,
      estimatedBill: (json['estimatedBill'] ?? 0.0).toDouble(),
      subscriber: json['subscriber'] != null
          ? Subscriber.fromJson(json['subscriber'])
          : null,
      subscriberName: json['subscriberName'],
      billNumber: json['billNumber'],
      billStatus: json['billStatus'],
      issueDate: json['issueDate'],
      dueDate: json['dueDate'],
    );
  }
}

// Subscriber is imported from user.dart
