import 'user.dart';

class CollectionRequest {
  final int subscriberId;
  final int userId;
  final double amount;
  final String paymentMethod;
  final String? notes;

  CollectionRequest({
    required this.subscriberId,
    required this.userId,
    required this.amount,
    this.paymentMethod = 'Cash',
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'subscriberId': subscriberId,
      'userId': userId,
      'amount': amount,
      'paymentMethod': paymentMethod,
      'notes': notes,
    };
  }
}

class CollectionResponse {
  final bool success;
  final String message;
  final int? transactionId;
  final double? newBalance;
  final DateTime? transactionDate;

  CollectionResponse({
    required this.success,
    required this.message,
    this.transactionId,
    this.newBalance,
    this.transactionDate,
  });

  factory CollectionResponse.fromJson(Map<String, dynamic> json) {
    return CollectionResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      transactionId: json['transactionId'],
      newBalance: json['newBalance']?.toDouble(),
      transactionDate: json['transactionDate'] != null
          ? DateTime.parse(json['transactionDate'])
          : null,
    );
  }
}

class BalanceInquiryRequest {
  final int subscriberId;

  BalanceInquiryRequest({
    required this.subscriberId,
  });

  Map<String, dynamic> toJson() {
    return {
      'subscriberId': subscriberId,
    };
  }
}

class BalanceInquiryResponse {
  final bool success;
  final String message;
  final double currentBalance;
  final double lastReading;
  final DateTime? lastReadingDate;
  final double estimatedBill;
  final Subscriber? subscriber;

  BalanceInquiryResponse({
    required this.success,
    required this.message,
    required this.currentBalance,
    required this.lastReading,
    this.lastReadingDate,
    required this.estimatedBill,
    this.subscriber,
  });

  factory BalanceInquiryResponse.fromJson(Map<String, dynamic> json) {
    return BalanceInquiryResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      currentBalance: (json['currentBalance'] ?? 0.0).toDouble(),
      lastReading: (json['lastReading'] ?? 0.0).toDouble(),
      lastReadingDate: json['lastReadingDate'] != null
          ? DateTime.parse(json['lastReadingDate'])
          : null,
      estimatedBill: (json['estimatedBill'] ?? 0.0).toDouble(),
      subscriber: json['subscriber'] != null
          ? Subscriber.fromJson(json['subscriber'])
          : null,
    );
  }
}

// Subscriber is imported from user.dart
