import 'user.dart';

class MeterReadingRequest {
  final int subscriberId;
  final int userId;
  final String meterNumber;
  final double currentReading;
  final double? previousReading;
  final String? imagePath;
  final String? meterImageBase64;
  final String? notes;
  final DateTime readingDate;

  MeterReadingRequest({
    required this.subscriberId,
    required this.userId,
    required this.meterNumber,
    required this.currentReading,
    this.previousReading,
    this.imagePath,
    this.meterImageBase64,
    this.notes,
    DateTime? readingDate,
  }) : readingDate = readingDate ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'subscriberId': subscriberId,
      'userId': userId,
      'meterNumber': meterNumber,
      'currentReading': currentReading,
      'previousReading': previousReading,
      'imagePath': imagePath,
      'meterImageBase64': meterImageBase64,
      'notes': notes,
      'readingDate': readingDate.toIso8601String(),
    };
  }
}

class MeterReadingResponse {
  final bool success;
  final String message;
  final int? readingId;
  final double? previousReading;
  final double? currentReading;
  final double? consumption;
  final double? estimatedBill;
  final DateTime? readingDate;
  final String? imagePath;

  MeterReadingResponse({
    required this.success,
    required this.message,
    this.readingId,
    this.previousReading,
    this.currentReading,
    this.consumption,
    this.estimatedBill,
    this.readingDate,
    this.imagePath,
  });

  factory MeterReadingResponse.fromJson(Map<String, dynamic> json) {
    return MeterReadingResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      readingId: json['readingId'],
      previousReading: json['previousReading']?.toDouble(),
      currentReading: json['currentReading']?.toDouble(),
      consumption: json['consumption']?.toDouble(),
      estimatedBill: json['estimatedBill']?.toDouble(),
      readingDate: json['readingDate'] != null
          ? DateTime.parse(json['readingDate'])
          : null,
      imagePath: json['imagePath'],
    );
  }
}

class MeterLookupRequest {
  final String meterNumber;

  MeterLookupRequest({
    required this.meterNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'meterNumber': meterNumber,
    };
  }
}

class MeterLookupResponse {
  final bool success;
  final String message;
  final Subscriber? subscriber;
  final double? lastReading;
  final DateTime? lastReadingDate;

  MeterLookupResponse({
    required this.success,
    required this.message,
    this.subscriber,
    this.lastReading,
    this.lastReadingDate,
  });

  factory MeterLookupResponse.fromJson(Map<String, dynamic> json) {
    return MeterLookupResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      subscriber: json['subscriber'] != null
          ? Subscriber.fromJson(json['subscriber'])
          : null,
      lastReading: json['lastReading']?.toDouble(),
      lastReadingDate: json['lastReadingDate'] != null
          ? DateTime.parse(json['lastReadingDate'])
          : null,
    );
  }
}
