class ChangePasswordRequest {
  final String username;
  final String oldpass;
  final String newpass;

  ChangePasswordRequest({
    required this.username,
    required this.oldpass,
    required this.newpass,
  });

  Map<String, dynamic> toJson() {
    return {
      'UserName': username,
      'oldpass': oldpass,
      'newpass': newpass,
    };
  }
}

class ChangePasswordResponse {
  final bool success;
  final String message;
  final String errorCode;

  ChangePasswordResponse({
    required this.success,
    required this.message,
    required this.errorCode,
  });

  factory ChangePasswordResponse.fromJson(Map<String, dynamic> json) {
    return ChangePasswordResponse(
      success: json['Error_Code'] == '000',
      message: json['Error_Description'] ?? '',
      errorCode: json['Error_Code'] ?? '',
    );
  }
}
