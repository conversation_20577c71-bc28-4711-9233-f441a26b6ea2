import 'package:elecollect_app/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:convert';

import '../models/user.dart';
import '../models/meter_reading.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import 'qr_scanner_screen.dart';

class MeterReadingScreen extends StatefulWidget {
  final String? prefilledMeterNumber;
  final Subscriber? subscriberInfo;

  const MeterReadingScreen({
    super.key,
    this.prefilledMeterNumber,
    this.subscriberInfo,
  });

  @override
  State<MeterReadingScreen> createState() => _MeterReadingScreenState();
}

class _MeterReadingScreenState extends State<MeterReadingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _meterNumberController = TextEditingController();
  final _currentReadingController = TextEditingController();
  final _notesController = TextEditingController();
  
  bool _isLoading = false;
  bool _isLookingUp = false;
  User? _currentUser;
  MeterLookupResponse? _subscriberInfo;
  XFile? _selectedImage;
  String? _imageBase64;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
    _initializeWithPrefilledData();
  }

  void _initializeWithPrefilledData() {
    // إذا تم تمرير رقم العداد ومعلومات المشترك
    if (widget.prefilledMeterNumber != null) {
      _meterNumberController.text = widget.prefilledMeterNumber!;

      // إذا تم تمرير معلومات المشترك أيضاً، استخدمها مباشرة
      if (widget.subscriberInfo != null) {
        setState(() {
          _subscriberInfo = MeterLookupResponse(
            success: true,
            message: 'تم العثور على المشترك',
            subscriber: widget.subscriberInfo,
            lastReading: widget.subscriberInfo!.lastReading,
            lastReadingDate: DateTime.now(),
          );
        });
      } else {
        // البحث عن المعلومات باستخدام رقم العداد
        _lookupMeter();
      }
    }
  }

  Future<void> _loadCurrentUser() async {
    final user = await StorageService.getUser();
    setState(() {
      _currentUser = user;
    });
  }

  Future<void> _scanQRCode() async {
    try {
      // Clear any previous data before scanning
      setState(() {
        _subscriberInfo = null;
      });

      final String? scannedValue = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) => const QRScannerScreen(),
        ),
      );

      if (scannedValue != null && scannedValue.isNotEmpty && mounted) {
        setState(() {
          _meterNumberController.text = scannedValue;
        });

        // Add a small delay to ensure UI is updated
        await Future.delayed(const Duration(milliseconds: 100));

        // Auto-search after scanning
        if (mounted) {
          _lookupMeter();
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في مسح الباركود: ${e.toString()}');
      }
    }
  }

  Future<void> _lookupMeter() async {
    if (_meterNumberController.text.trim().isEmpty) {
      _showErrorSnackBar('يرجى إدخال رقم العداد');
      return;
    }

    // Prevent multiple simultaneous lookups
    if (_isLookingUp) return;

    setState(() {
      _isLookingUp = true;
      _subscriberInfo = null;
    });

    try {
      final request = MeterLookupRequest(
        meterNumber: _meterNumberController.text.trim(),
      );

      final response = await ApiService.lookupMeter(request);

      if (mounted) {
        setState(() {
          _subscriberInfo = response;
          _isLookingUp = false;
        });

        if (!response.success) {
          _showErrorSnackBar(response.message);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLookingUp = false;
        });
        _showErrorSnackBar('خطأ في الاتصال: ${e.toString()}');
      }
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (image != null) {
        // استخدام readAsBytes() مباشرة من XFile بدلاً من File
        // هذا يعمل على جميع المنصات بما في ذلك الويب
        final bytes = await image.readAsBytes();
        final base64Image = base64Encode(bytes);

        setState(() {
          // حفظ XFile بدلاً من File للتوافق مع الويب
          _selectedImage = image;
          _imageBase64 = base64Image;
        });
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار الصورة: ${e.toString()}');
    }
  }

  void _showImageSourceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار مصدر الصورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('الكاميرا'),
              onTap: () {
                Navigator.of(context).pop();
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('المعرض'),
              onTap: () {
                Navigator.of(context).pop();
                _pickImage(ImageSource.gallery);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitReading() async {
    if (!_formKey.currentState!.validate() || _currentUser == null) return;

    if (_subscriberInfo == null || !_subscriberInfo!.success) {
      _showErrorSnackBar('يرجى البحث عن العداد أولاً');
      return;
    }

    final currentReading = double.tryParse(_currentReadingController.text);
    if (currentReading == null) {
      _showErrorSnackBar('يرجى إدخال قراءة صحيحة');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = MeterReadingRequest(
        subscriberId: _subscriberInfo!.subscriber!.id,
        userId: _currentUser!.id,
        meterNumber: _meterNumberController.text.trim(),
        currentReading: currentReading,
        previousReading: _subscriberInfo!.lastReading,
        meterImageBase64: _imageBase64,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      final response = await ApiService.processMeterReading(request);

      setState(() {
        _isLoading = false;
      });

      if (response.success) {
        _showSuccessDialog(response);
      } else {
        _showErrorSnackBar(response.message);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('خطأ في الاتصال: ${e.toString()}');
    }
  }

  void _showSuccessDialog(MeterReadingResponse response) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('تم تسجيل القراءة بنجاح'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (response.readingId != null)
              Text('رقم القراءة: ${response.readingId}'),
            const SizedBox(height: 8),
            if (response.previousReading != null)
              Text('القراءة السابقة: ${response.previousReading!.toStringAsFixed(2)}'),
            const SizedBox(height: 8),
            if (response.currentReading != null)
              Text('القراءة الحالية: ${response.currentReading!.toStringAsFixed(2)}'),
            const SizedBox(height: 8),
            if (response.consumption != null)
              Text('الاستهلاك: ${response.consumption!.toStringAsFixed(2)}'),
            const SizedBox(height: 8),
            if (response.estimatedBill != null)
              Text('الفاتورة المقدرة: ${response.estimatedBill!.toStringAsFixed(2)} ريال'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(); // العودة للشاشة السابقة
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Text('قراءة العداد'),
            if (widget.prefilledMeterNumber != null) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.withValues(alpha: 0.5)),
                ),
                child: const Text(
                  'تم التحديد مسبقاً',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        backgroundColor: AppTheme.themeData.primaryColor ,//Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // البحث عن العداد
              _buildMeterLookupSection(),
              const SizedBox(height: 20),
              
              // معلومات المشترك
              if (_subscriberInfo != null && _subscriberInfo!.success)
                _buildSubscriberInfo(),
              const SizedBox(height: 20),
              
              // نموذج القراءة
              if (_subscriberInfo != null && _subscriberInfo!.success)
                _buildReadingForm(),
              const SizedBox(height: 20),
              
              // صورة العداد
              if (_subscriberInfo != null && _subscriberInfo!.success)
                _buildImageSection(),
              const SizedBox(height: 30),
              
              // زر الإرسال
              if (_subscriberInfo != null && _subscriberInfo!.success)
                _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMeterLookupSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'البحث عن العداد',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _meterNumberController,
                    decoration: InputDecoration(
                      labelText: 'رقم العداد *',
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.qr_code_scanner),
                        onPressed: _scanQRCode,
                        tooltip: 'مسح الباركود/QR',
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال رقم العداد';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _isLookingUp ? null : _lookupMeter,
                  child: _isLookingUp
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('بحث'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'يمكنك مسح الباركود أو QR أو إدخال رقم العداد يدوياً',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriberInfo() {
    final subscriber = _subscriberInfo!.subscriber!;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المشترك',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('رقم المشترك:', subscriber.subscriberNumber),
            _buildInfoRow('اسم المشترك:', subscriber.subscriberName),
            _buildInfoRow('العنوان:', subscriber.address ?? 'غير محدد'),
            if (subscriber.phone != null)
              _buildInfoRow('الهاتف:', subscriber.phone!),
            if (_subscriberInfo!.lastReading != null)
              _buildInfoRow(
                'آخر قراءة:', 
                _subscriberInfo!.lastReading!.toStringAsFixed(2),
              ),
            if (_subscriberInfo!.lastReadingDate != null)
              _buildInfoRow(
                'تاريخ آخر قراءة:', 
                _subscriberInfo!.lastReadingDate!.toString().split(' ')[0],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildReadingForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'القراءة الحالية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _currentReadingController,
              decoration: const InputDecoration(
                labelText: 'القراءة الحالية *',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال القراءة الحالية';
                }
                final reading = double.tryParse(value);
                if (reading == null || reading < 0) {
                  return 'يرجى إدخال قراءة صحيحة';
                }
                if (_subscriberInfo?.lastReading != null && 
                    reading < _subscriberInfo!.lastReading!) {
                  return 'القراءة الحالية يجب أن تكون أكبر من آخر قراءة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'صورة العداد',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            if (_selectedImage != null) ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: FutureBuilder<Uint8List>(
                    future: _selectedImage!.readAsBytes(),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Image.memory(
                          snapshot.data!,
                          fit: BoxFit.cover,
                        );
                      } else {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _showImageSourceDialog,
                      icon: const Icon(Icons.edit),
                      label: const Text('تغيير الصورة'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        setState(() {
                          _selectedImage = null;
                          _imageBase64 = null;
                        });
                      },
                      icon: const Icon(Icons.delete),
                      label: const Text('حذف الصورة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: double.infinity,
                height: 150,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey, style: BorderStyle.solid),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: InkWell(
                  onTap: _showImageSourceDialog,
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_a_photo, size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text('اضغط لإضافة صورة العداد'),
                      Text('(اختياري)', style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submitReading,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
        ),
        child: _isLoading
          ? const CircularProgressIndicator(color: Colors.white)
          : const Text(
              'إرسال القراءة الحالية',
              style: TextStyle(fontSize: 16),
            ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _meterNumberController.dispose();
    _currentReadingController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
