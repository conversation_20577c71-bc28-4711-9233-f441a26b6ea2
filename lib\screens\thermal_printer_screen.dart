import 'package:flutter/material.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import '../services/bluetooth_thermal_printer.dart';
import '../theme/app_colors.dart';
import '../theme/app_styles.dart';

class ThermalPrinterScreen extends StatefulWidget {
  const ThermalPrinterScreen({super.key});

  @override
  State<ThermalPrinterScreen> createState() => _ThermalPrinterScreenState();
}

class _ThermalPrinterScreenState extends State<ThermalPrinterScreen> {
  List<BluetoothInfo> _printers = [];
  bool _isScanning = false;
  bool _isConnecting = false;
  BluetoothInfo? _selectedPrinter;

  @override
  void initState() {
    super.initState();
    _scanForPrinters();
  }

  Future<void> _scanForPrinters() async {
    if (_isScanning) return;

    setState(() {
      _isScanning = true;
      _printers.clear();
    });

    try {

    // await BluetoothThermalPrinter.disconnect();
      final printers = await BluetoothThermalPrinter.getAvailablePrinters();
      if (mounted) {
        setState(() {
          _printers = printers;
          _isScanning = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
        _showErrorSnackBar('خطأ في البحث عن الطابعات: ${e.toString()}');
      }
    }
  }

  Future<void> _connectToPrinter(BluetoothInfo printer) async {
    if (_isConnecting) return;

    setState(() {
      _isConnecting = true;
    });

    try {
      final success = await BluetoothThermalPrinter.connectToPrinter(printer);

      if (mounted) {
        setState(() {
          _isConnecting = false;
        });

        if (success) {
          setState(() {
            _selectedPrinter = printer;
          });
          _showSuccessSnackBar('تم الاتصال بالطابعة بنجاح');

          // Test print
          _testPrint();
        } else {
          _showErrorSnackBar('فشل في الاتصال بالطابعة');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isConnecting = false;
        });
        _showErrorSnackBar('خطأ في الاتصال: ${e.toString()}');
      }
    }
  }

  Future<void> _disconnect() async {
    try {
      await BluetoothThermalPrinter.disconnect();
      if (mounted) {
        setState(() {
          _selectedPrinter = null;
        });
        _showSuccessSnackBar('تم قطع الاتصال');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في قطع الاتصال: ${e.toString()}');
    }
  }

  Future<void> _testPrint() async {
    try {
      final success = await BluetoothThermalPrinter.testPrint();
      if (success) {
        _showSuccessSnackBar('تم اختبار الطباعة بنجاح');
      } else {
        _showErrorSnackBar('فشل في اختبار الطباعة');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختبار الطباعة: ${e.toString()}');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الطابعات الحرارية'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnDark,
        actions: [
          IconButton(
            onPressed: _isScanning ? null : _scanForPrinters,
            icon: _isScanning 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Icon(Icons.refresh),
            tooltip: 'إعادة البحث',
          ),
        ],
      ),
      body: Column(
        children: [
          // Connection status
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: BluetoothThermalPrinter.isConnected() ? AppColors.success : AppColors.error,
            child: Row(
              children: [
                Icon(
                  BluetoothThermalPrinter.isConnected()
                      ? Icons.bluetooth_connected
                      : Icons.bluetooth_disabled,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Text(
                  BluetoothThermalPrinter.isConnected()
                      ? 'متصل بالطابعة الحرارية'
                      : 'غير متصل',
                  style: AppStyles.textOnDark,
                ),
                const Spacer(),
                if (BluetoothThermalPrinter.isConnected())
                  TextButton(
                    onPressed: _disconnect,
                    child: const Text(
                      'قطع الاتصال',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
              ],
            ),
          ),

          // Scanning indicator
          if (_isScanning)
            Container(
              padding: const EdgeInsets.all(16),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('البحث عن الطابعات...'),
                ],
              ),
            ),

          // Printers list
          Expanded(
            child: _printers.isEmpty && !_isScanning
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.print_disabled,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد طابعات متاحة',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'تأكد من تشغيل البلوتوث وأن الطابعة في وضع الاقتران',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _printers.length,
                    itemBuilder: (context, index) {
                      final printer = _printers[index];
                      final isConnected = _selectedPrinter?.macAdress == printer.macAdress;
                      
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: Icon(
                            isConnected 
                                ? Icons.print 
                                : Icons.print_outlined,
                            color: isConnected 
                                ? AppColors.success 
                                : AppColors.primary,
                          ),
                          title: Text(
                            printer.name,
                            style: AppStyles.bodyLarge,
                          ),
                          subtitle: Text(
                            printer.macAdress,
                            style: AppStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          trailing: isConnected
                              ? Chip(
                                  label: const Text('متصل'),
                                  backgroundColor: AppColors.success,
                                  labelStyle: const TextStyle(color: Colors.white),
                                )
                              : _isConnecting
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : ElevatedButton(
                                      onPressed: () => _connectToPrinter(printer),
                                      style: AppStyles.primaryButton,
                                      child: const Text('اتصال'),
                                    ),
                        ),
                      );
                    },
                  ),
          ),

          // Test print button
          if (BluetoothThermalPrinter.isConnected())
            Padding(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _testPrint,
                  icon: const Icon(Icons.print),
                  label: const Text('اختبار الطباعة'),
                  style: AppStyles.secondaryButton,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
