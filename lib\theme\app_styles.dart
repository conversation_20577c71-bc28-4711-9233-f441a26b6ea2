import 'package:flutter/material.dart';
import 'app_colors.dart';

/// نظام الأنماط الشامل للتطبيق
/// يحتوي على جميع أنماط النصوص والأزرار والبطاقات
class AppStyles {
  // ========== أنماط النصوص ==========
  
  /// نمط العنوان الرئيسي
  static const TextStyle headingLarge = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    height: 1.2,
  );
  
  /// نمط العنوان المتوسط
  static const TextStyle headingMedium = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    height: 1.3,
  );
  
  /// نمط العنوان الصغير
  static const TextStyle headingSmall = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    height: 1.4,
  );
  
  /// نمط النص الأساسي
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    height: 1.5,
  );
  
  /// نمط النص المتوسط
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    height: 1.5,
  );
  
  /// نمط النص الصغير
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    height: 1.4,
  );
  
  /// نمط النص على الخلفية الداكنة
  static const TextStyle textOnDark = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textOnDark,
    height: 1.5,
  );
  
  /// نمط النص المميز
  static const TextStyle highlight = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.primary,
    height: 1.5,
  );

  // ========== أنماط الأزرار ==========
  
  /// نمط الزر الأساسي
  static ButtonStyle primaryButton = ElevatedButton.styleFrom(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.textOnDark,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
    elevation: 2,
  );
  
  /// نمط الزر الثانوي
  static ButtonStyle secondaryButton = ElevatedButton.styleFrom(
    backgroundColor: AppColors.secondary,
    foregroundColor: AppColors.textOnDark,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
    elevation: 2,
  );
  
  /// نمط الزر المحدد
  static ButtonStyle outlinedButton = OutlinedButton.styleFrom(
    foregroundColor: AppColors.primary,
    side: const BorderSide(color: AppColors.primary, width: 1),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  );
  
  /// نمط الزر النصي
  static ButtonStyle textButton = TextButton.styleFrom(
    foregroundColor: AppColors.primary,
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  );

  // ========== أنماط البطاقات ==========
  
  /// نمط البطاقة الأساسية
  static BoxDecoration cardDecoration = BoxDecoration(
    color: AppColors.cardBackground,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowLight,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  );
  
  /// نمط البطاقة المرفوعة
  static BoxDecoration elevatedCardDecoration = BoxDecoration(
    color: AppColors.cardBackground,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowMedium,
        blurRadius: 16,
        offset: const Offset(0, 4),
      ),
    ],
  );
  
  /// نمط البطاقة المحددة
  static BoxDecoration selectedCardDecoration = BoxDecoration(
    color: AppColors.cardBackground,
    borderRadius: BorderRadius.circular(12),
    border: Border.all(color: AppColors.primary, width: 2),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowLight,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  );

  // ========== أنماط الحقول ==========
  
  /// نمط حقل الإدخال
  static InputDecoration inputDecoration(String label, {String? hint, IconData? icon}) {
    return InputDecoration(
      labelText: label,
      hintText: hint,
      prefixIcon: icon != null ? Icon(icon, color: AppColors.textSecondary) : null,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.border),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.borderFocused, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppColors.borderError, width: 2),
      ),
      filled: true,
      fillColor: AppColors.cardBackground,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  // ========== أنماط AppBar ==========
  
  /// نمط AppBar الأساسي
  static AppBarTheme appBarTheme = const AppBarTheme(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.textOnDark,
    elevation: 4,
    centerTitle: true,
    titleTextStyle: TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: AppColors.textOnDark,
    ),
  );

  // ========== أنماط الحالات ==========
  
  /// نمط حالة النجاح
  static BoxDecoration successDecoration = BoxDecoration(
    color: AppColors.withOpacity(AppColors.success, 1),
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: AppColors.success, width: 1),
  );
  
  /// نمط حالة التحذير
  static BoxDecoration warningDecoration = BoxDecoration(
    color: AppColors.withOpacity(AppColors.warning, 1),
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: AppColors.warning, width: 1),
  );
  
  /// نمط حالة الخطأ
  static BoxDecoration errorDecoration = BoxDecoration(
    color: AppColors.withOpacity(AppColors.error, 1),
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: AppColors.error, width: 1),
  );

  // ========== أنماط خاصة بالتطبيق ==========
  
  /// نمط بطاقة المشترك
  static BoxDecoration subscriberCardDecoration = BoxDecoration(
    color: AppColors.cardBackground,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowLight,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
    border: Border.all(color: AppColors.border, width: 0.5),
  );
  
  /// نمط بطاقة التحصيل
  static BoxDecoration collectionCardDecoration = BoxDecoration(
    color: AppColors.cardBackground,
    borderRadius: BorderRadius.circular(12),
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        AppColors.cardBackground,
        AppColors.withOpacity(AppColors.primary, 05),
      ],
    ),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowMedium,
        blurRadius: 12,
        offset: const Offset(0, 3),
      ),
    ],
  );
  
  /// نمط بطاقة التقرير
  static BoxDecoration reportCardDecoration = BoxDecoration(
    color: AppColors.cardBackground,
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowMedium,
        blurRadius: 16,
        offset: const Offset(0, 4),
      ),
    ],
  );

  // ========== دوال مساعدة ==========
  
  /// الحصول على نمط نص ملون
  static TextStyle getColoredTextStyle(Color color, {double? fontSize, FontWeight? fontWeight}) {
    return TextStyle(
      color: color,
      fontSize: fontSize ?? 16,
      fontWeight: fontWeight ?? FontWeight.normal,
      height: 1.5,
    );
  }
  
  /// الحصول على نمط زر ملون
  static ButtonStyle getColoredButtonStyle(Color backgroundColor, Color foregroundColor) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 2,
    );
  }
  
  /// الحصول على نمط بطاقة ملونة
  static BoxDecoration getColoredCardDecoration(Color color, {int opacity = 1}) {
    return BoxDecoration(
      color: AppColors.withOpacity(color, opacity),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: color, width: 1),
      boxShadow: [
        BoxShadow(
          color: AppColors.shadowLight,
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }
}
