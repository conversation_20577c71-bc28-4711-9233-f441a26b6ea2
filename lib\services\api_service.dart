import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/user.dart';
import '../models/collection.dart';
import '../models/collection_report.dart';
import '../models/meter_reading.dart';

class ApiService {
  static const String baseUrl = 'http://195.94.15.173:9001/api';

  static const Duration timeoutDuration = Duration(seconds: 30);

  // متغير لحفظ التوكن
  static String? _authToken;

  // حفظ التوكن
  static void setAuthToken(String token) {
    _authToken = token;
  }

  // الحصول على التوكن
  static String? getAuthToken() {
    return _authToken;
  }


  

  // تسجيل الدخول
  static Future<LoginResponse> login(LoginRequest request) async {
    print("تسجيل الدخول");
    try {

       print(jsonEncode(request.toJson()));
        print('$baseUrl/auth/Biller_Bill_Login');

      final response = await http.post(
        Uri.parse('$baseUrl/auth/Biller_<PERSON>_Login'),
        headers: {
           'Content-Type': 'application/json',

        },
        // body: jsonEncode({"UserName":  request.username , "UserPass": request.password }),
          body: jsonEncode(request.toJson()),
      ).timeout(timeoutDuration);

print(response.statusCode);
print(response.body);
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['ErrorCode'] == '0000') {
          // حفظ التوكن
          _authToken = responseData['TokenKey'];

          // إنشاء كائن المستخدم من الاستجابة
          final user = User(
            id: responseData['region_no'] ?? 0,
            username: request.username,
            fullName: responseData['name'] ?? '',
            permissions: _getPermissionsFromPrivilege(responseData['privilege']),
          );

          return LoginResponse(
            success: true,
            message: responseData['ErrorDescription'] ?? 'تم تسجيل الدخول بنجاح',
            user: user,
          );
        } else {
          return LoginResponse(
            success: false,
            message: responseData['ErrorDescription'] ?? 'خطأ في تسجيل الدخول',
          );
        }
      }

      return LoginResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
      );
    } catch (e) {
      return LoginResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
      );
    }
  }

  // تحويل صلاحيات API إلى صلاحيات التطبيق
  static List<String> _getPermissionsFromPrivilege(Map<String, dynamic>? privilege) {
    List<String> permissions = [];

    if (privilege != null) {
      if (privilege['meterRead'] == true) {
        permissions.addAll(['view_subscribers', 'meter_reading']);
      }
      if (privilege['meterPay'] == true) {
        permissions.addAll(['view_subscribers', 'collection', 'reports']);
      }
    }

    return permissions;
  }

  // جلب المشتركين
  static Future<List<Subscriber>> getSubscribers({String? search}) async {
    print("جلب المشتركين");
    try {
      if (_authToken == null) {
        print("لا يوجد توكن مصادقة");
        return [];
      }

      final response = await http.post(
        Uri.parse('$baseUrl/billers/Upload_Data'),
        headers: {
          'Content-Type': 'application/json',
          'Token': _authToken!,
        },
        body: jsonEncode({"Datatype": "1"}),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['Error_Code'] == '000') {
          final List<dynamic> dataArray = responseData['DataArray'] ?? [];

          List<Subscriber> subscribers = dataArray.map((item) {
            return Subscriber(
              id: item['partnerid'] ?? 0,
              subscriberNumber: item['fax']?.toString() ?? '',
              subscriberName: item['name'] ?? '',
              address: null,
              phone: null,
              meterNumber: item['nickname']?.toString(),
              currentBalance: (item['upper'] ?? 0).toDouble(),
              lastReading: (item['lower'] ?? 0).toDouble(),
              lastReadingDate: null,
            );
          }).toList();

          // تطبيق البحث إذا كان موجوداً
          if (search != null && search.isNotEmpty) {
            subscribers = subscribers.where((subscriber) {
              return subscriber.subscriberName.toLowerCase().contains(search.toLowerCase()) ||
                     subscriber.subscriberNumber.contains(search) ||
                     (subscriber.meterNumber?.contains(search) ?? false);
            }).toList();
          }

          return subscribers;
        }
      }

      return [];
    } catch (e) {
      print("خطأ في جلب المشتركين: ${e.toString()}");
      return [];
    }
  }

  // الاستعلام عن الرصيد
  static Future<BalanceInquiryResponse> inquireBalance(BalanceInquiryRequest request) async {
    print("الاستعلام عن الرصيد");
    try {
      if (_authToken == null) {
        return BalanceInquiryResponse(
          success: false,
          message: 'لا يوجد توكن مصادقة',
          currentBalance: 0,
          lastReading: 0,
          estimatedBill: 0,
        );
      }

      final response = await http.post(
        Uri.parse('$baseUrl/billers/Biller_Bill_Inquiry'),
        headers: {
          'Content-Type': 'application/json',
          'Token': _authToken!,
        },
        body: jsonEncode({"Billing_No": request.subscriberNumber}),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['Error_Code'] == '000') {
          final List<dynamic> billingsRec = responseData['BillingsRec'] ?? [];

          if (billingsRec.isNotEmpty) {
            final billData = billingsRec.first;

            return BalanceInquiryResponse(
              success: true,
              message: billData['Message'] ?? responseData['Error_Description'] ?? 'تم الاستعلام بنجاح',
              currentBalance: double.tryParse(billData['Due_Amount']?.toString() ?? '0') ?? 0,
              lastReading: double.tryParse(billData['LastReadingMeter']?.toString() ?? '0') ?? 0,
              estimatedBill: double.tryParse(billData['Due_Amount']?.toString() ?? '0') ?? 0,
              subscriberName: billData['SubscribeName'],
              billNumber: billData['Bill_No']?.toString(),
              billStatus: billData['BillStatus'],
              issueDate: billData['Issue_Date'],
              dueDate: billData['Due_Date'],
            );
          }
        }

        return BalanceInquiryResponse(
          success: false,
          message: responseData['Error_Description'] ?? 'لم يتم العثور على بيانات',
          currentBalance: 0,
          lastReading: 0,
          estimatedBill: 0,
        );
      }

      return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
      );
    } catch (e) {
      return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
      );
    }
  }

  // معالجة التحصيل
  static Future<CollectionResponse> processCollection(CollectionRequest request) async {
    print("معالجة التحصيل");
    try {
      if (_authToken == null) {
        return CollectionResponse(
          success: false,
          message: 'لا يوجد توكن مصادقة',
        );
      }

      final response = await http.post(
        Uri.parse('$baseUrl/billers/Biller_Bill_Payment'),
        headers: {
          'Content-Type': 'application/json',
          'Token': _authToken!,
        },
        body: jsonEncode(request.toJson()),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['Error_Code'] == '000') {
          return CollectionResponse(
            success: true,
            message: responseData['Error_Description'] ?? 'تم التحصيل بنجاح',
            transactionId: int.tryParse(request.transactionId),
            transactionDate: DateTime.now(),
          );
        } else {
          return CollectionResponse(
            success: false,
            message: responseData['Error_Description'] ?? 'خطأ في معالجة التحصيل',
          );
        }
      }

      return CollectionResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
      );
    } catch (e) {
      return CollectionResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
      );
    }
  }

  // دالة مساعدة للاستعلام عن الرصيد (نفس inquireBalance)
  static Future<BalanceInquiryResponse> getBalance(BalanceInquiryRequest request) async {
    return inquireBalance(request);
  }

  // البحث عن عداد (يستخدم البيانات المحلية)
  static Future<MeterLookupResponse> lookupMeter(MeterLookupRequest request) async {
    print("البحث عن عداد");
    try {
      // البحث في البيانات المحلية للمشتركين
      final subscribers = await getSubscribers();

      final subscriber = subscribers.firstWhere(
        (s) => s.meterNumber == request.meterNumber || s.subscriberNumber == request.meterNumber,
        orElse: () => throw Exception('لم يتم العثور على العداد'),
      );

      return MeterLookupResponse(
        success: true,
        message: 'تم العثور على العداد',
        subscriber: subscriber,
        lastReading: subscriber.lastReading,
        lastReadingDate: subscriber.lastReadingDate,
      );
    } catch (e) {
      return MeterLookupResponse(
        success: false,
        message: 'لم يتم العثور على العداد: ${e.toString()}',
      );
    }
  }

  // معالجة قراءة العداد (حفظ محلي)
  static Future<MeterReadingResponse> processMeterReading(MeterReadingRequest request) async {
    print("معالجة قراءة العداد");
    try {
      // حساب الاستهلاك
      final consumption = request.previousReading != null
          ? request.currentReading - request.previousReading!
          : 0.0;

      // تقدير الفاتورة (يمكن تخصيص هذا حسب التعرفة)
      final estimatedBill = consumption * 0.5; // مثال: 0.5 ريال لكل وحدة

      // في التطبيق الحقيقي، يجب حفظ هذه البيانات في قاعدة البيانات المحلية
      // هنا سنرجع استجابة نجاح

      return MeterReadingResponse(
        success: true,
        message: 'تم حفظ قراءة العداد بنجاح',
        readingId: DateTime.now().millisecondsSinceEpoch,
        previousReading: request.previousReading,
        currentReading: request.currentReading,
        consumption: consumption,
        estimatedBill: estimatedBill,
        readingDate: request.readingDate,
      );
    } catch (e) {
      return MeterReadingResponse(
        success: false,
        message: 'خطأ في معالجة قراءة العداد: ${e.toString()}',
      );
    }
  }

  // Get collection reports (البيانات المحلية)
  static Future<CollectionReportResponse> getCollectionReports({
    String? subscriberNumber,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    print("تقرير التحصيلات");
    try {
      // في التطبيق الحقيقي، يجب جلب البيانات من قاعدة البيانات المحلية
      // هنا سنرجع قائمة فارغة أو بيانات تجريبية

      return CollectionReportResponse(
        success: true,
        message: 'تم جلب التقارير بنجاح',
        collections: [], // قائمة فارغة حالياً
      );
    } catch (e) {
      return CollectionReportResponse(
        success: false,
        message: 'خطأ في جلب تقارير التحصيلات: ${e.toString()}',
        collections: [],
      );
    }
  }
}
