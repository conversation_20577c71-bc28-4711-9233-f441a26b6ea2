import 'dart:convert';
import 'package:http/http.dart' as http;
import 'encryption_service.dart';
import '../models/user.dart';
import '../models/collection.dart';
import '../models/collection_report.dart';
import '../models/meter_reading.dart';

class ApiService {
  static const String baseUrl = 'http://195.94.15.173:7001/api'; // تغيير هذا للخادم الفعلي
  
  static const Duration timeoutDuration = Duration(seconds: 30);

  // تسجيل الدخول
  static Future<LoginResponse> login(LoginRequest request) async {
    print("   تسجيل الدخول");
    try {
      final encryptedRequest = EncryptionService.encryptObject(request.toJson());

      final response = await http.post(
        Uri.parse('$baseUrl/authsimple/login'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: '"$encryptedRequest"',
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final encryptedResponse = response.body.replaceAll('"', '');
        final responseData = EncryptionService.decryptToMap(encryptedResponse);
        
        if (responseData != null) {
          return LoginResponse.fromJson(responseData);
        }
      }
      print(response.statusCode);
      print(response.body);
      return LoginResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
      );
    } catch (e) {
      return LoginResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
      );
    }
  }

  // جلب المشتركين
  static Future<List<Subscriber>> getSubscribers({String? search}) async {
     print("  جلب المشتركين");
    try {
      String url = '$baseUrl/auth/subscribers';
      if (search != null && search.isNotEmpty) {
         print(search);
        url += '?search=$search';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(timeoutDuration);
//print(response.statusCode );
//print(response.body );
      if (response.statusCode == 200) {
        final encryptedResponse = response.body.replaceAll('"', '');
       // print(encryptedResponse);
        final subscribers = EncryptionService.decryptToList<Subscriber>(
          encryptedResponse,
          (json) => Subscriber.fromJson(json),
        );
        // print(subscribers);
        return subscribers ?? [];
      }
      
      return [];
    } catch (e) {
      return [];
    }
  }

  // الاستعلام عن الرصيد
  static Future<BalanceInquiryResponse> inquireBalance(BalanceInquiryRequest request) async {
       print(" inquireBalance الاستعلام عن الرصيد");
    try {
      final encryptedRequest = EncryptionService.encryptObject(request.toJson());

      final response = await http.post(
        Uri.parse('$baseUrl/collection/balance-inquiry'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: '"$encryptedRequest"',
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final encryptedResponse = response.body.replaceAll('"', '');
        final responseData = EncryptionService.decryptToMap(encryptedResponse);

        if (responseData != null) {
          return BalanceInquiryResponse.fromJson(responseData);
        }
      }

      return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
      );
    } catch (e) {
      return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
      );
    }
  }

  // معالجة التحصيل
  static Future<CollectionResponse> processCollection(CollectionRequest request) async {
      print("  معالجة التحصيل");
    try {
      final encryptedRequest = EncryptionService.encryptObject(request.toJson());
      
      final response = await http.post(
        Uri.parse('$baseUrl/collection/process'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(encryptedRequest),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final encryptedResponse = response.body.replaceAll('"', '');
        final responseData = EncryptionService.decryptToMap(encryptedResponse);
        
        if (responseData != null) {
          return CollectionResponse.fromJson(responseData);
        }
      }
      
      return CollectionResponse(
        success: false,
        message: 'خطأ في معالجة التحصيل',
      );
    } catch (e) {
      return CollectionResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
      );
    }
  }

  // الاستعلام عن الرصيد
  static Future<BalanceInquiryResponse> getBalance(BalanceInquiryRequest request) async {
     print(" getBalance الاستعلام عن الرصيد ");
    try {
      final encryptedRequest = EncryptionService.encryptObject(request.toJson());
      
      final response = await http.post(
        Uri.parse('$baseUrl/collection/balance-inquiry'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(encryptedRequest),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final encryptedResponse = response.body.replaceAll('"', '');
        final responseData = EncryptionService.decryptToMap(encryptedResponse);
        
        if (responseData != null) {
          return BalanceInquiryResponse.fromJson(responseData);
        }
      }
      
      return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاستعلام عن الرصيد',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
      );
    } catch (e) {
      return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
      );
    }
  }

  // البحث عن عداد
  static Future<MeterLookupResponse> lookupMeter(MeterLookupRequest request) async {
      print(" البحث عن عداد");
    try {
      final encryptedRequest = EncryptionService.encryptObject(request.toJson());

      final response = await http.post(
        Uri.parse('$baseUrl/meterreading/lookup'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: '"$encryptedRequest"',
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final encryptedResponse = response.body.replaceAll('"', '');
        final responseData = EncryptionService.decryptToMap(encryptedResponse);

        if (responseData != null) {
          return MeterLookupResponse.fromJson(responseData);
        }
      }

      return MeterLookupResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
      );
    } catch (e) {
      return MeterLookupResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
      );
    }
  }

  // معالجة قراءة العداد
  static Future<MeterReadingResponse> processMeterReading(MeterReadingRequest request) async {
     print(" معالجة قراءة العداد");
    try {
      final encryptedRequest = EncryptionService.encryptObject(request.toJson());
      
      final response = await http.post(
        Uri.parse('$baseUrl/meterreading/process'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(encryptedRequest),
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final encryptedResponse = response.body.replaceAll('"', '');
        final responseData = EncryptionService.decryptToMap(encryptedResponse);
        
        if (responseData != null) {
          return MeterReadingResponse.fromJson(responseData);
        }
      }
      
      return MeterReadingResponse(
        success: false,
        message: 'خطأ في معالجة قراءة العداد',
      );
    } catch (e) {
      return MeterReadingResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
      );
    }
  }

  // Get collection reports
  static Future<CollectionReportResponse> getCollectionReports({
    String? subscriberNumber,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
      print(" تقرير التحصيلات");
    try {
      final Map<String, String> queryParams = {};

      if (subscriberNumber != null && subscriberNumber.isNotEmpty) {
        queryParams['subscriberNumber'] = subscriberNumber;
      }

      if (fromDate != null) {
        queryParams['fromDate'] = fromDate.toIso8601String();
      }

      if (toDate != null) {
        queryParams['toDate'] = toDate.toIso8601String();
      }
print(queryParams);
      final uri = Uri.parse('$baseUrl/reports/collections').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(timeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return CollectionReportResponse.fromJson(data);
      }

      return CollectionReportResponse(
        success: false,
        message: 'خطأ في جلب تقارير التحصيلات',
        collections: [],
      );
    } catch (e) {
      return CollectionReportResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
        collections: [],
      );
    }
  }
}
