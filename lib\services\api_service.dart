import 'dart:convert';
import 'package:elecollect_app/models/api_req_res.dart';
import 'package:elecollect_app/models/change_password.dart';
import 'package:elecollect_app/screens/login_screen.dart';
import 'package:elecollect_app/services/encryption_service.dart';
import 'package:elecollect_app/services/snackbar.dart';
import 'package:elecollect_app/services/storage_service.dart';
import 'package:http/http.dart' as http;
import '../models/user.dart';
import '../models/collection.dart';
import '../models/collection_report.dart';
import '../models/meter_reading.dart';
import 'package:flutter/material.dart';

class ApiService {
 // static const String baseUrl = 'http://195.94.15.173:9002/api';
   static const String baseUrl = 'http://195.94.15.159:9002/api';



  static const Duration timeoutDuration = Duration(seconds: 60);

  // متغير لحفظ التوكن
  static String? _authToken;

  // حفظ التوكن
  static void setAuthToken(String token) {
    _authToken = token;
  }

  // الحصول على التوكن
  static String? getAuthToken() {
    return _authToken;
  }


  

  // تسجيل الدخول
  static Future<LoginResponse> login(LoginRequest request) async {
    print("تسجيل الدخول");
    try {
      print("البيانات المرسلة: ${EncryptionService.encryptObject(request.toJson())}");
      print("الرابط: $baseUrl/auth/Biller_Bill_Login");

var _lastReq = ApiReqRes(
data: EncryptionService.encryptObject(request.toJson())
  
);
  print("البيانات المرسلة: ${jsonEncode(_lastReq.toJson())}");
      final response = await http.post(
        Uri.parse('$baseUrl/auth/Biller_Bill_Login'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(_lastReq.toJson()),
      ).timeout(timeoutDuration);

      print("رمز الاستجابة: ${response.statusCode}");
      print("محتوى الاستجابة: ${response.body}");
      if (response.statusCode == 200) {

       final responseData = EncryptionService.decryptToMap(response.body);
 print("محتوى الاستجابة: ${responseData}");
       // final responseData = jsonDecode(response.body);

        if (responseData!['Error_Code'] == '0000') {
          // حفظ التوكن
          _authToken = responseData['TokenKey'];

          // إنشاء كائن المستخدم من الاستجابة
          final user = User(
            id: responseData['userids'] ?? 0,
            username: request.username,
            fullName: responseData['name'] ?? '',
            permissions: _getPermissionsFromPrivilege(responseData['privilege']),
          );

          return LoginResponse(
            success: true,
            message: responseData['Error_Description'] ?? 'تم تسجيل الدخول بنجاح',
            user: user,
          );
        } else {
     if (responseData['Error_Code'] == "402") {
          // خطأ في ال API
        

            return LoginResponse(
            success: false,
            message: (responseData['Error_Description'] ?? ' خطأ في تسجيل الدخول' ) +   ' يرجى التواصل مع الإدارة ' ,
          );
        }  else if (responseData['Error_Code'].toString() == "508" || responseData['Error_Code'].toString() == "509") {
          // خطأ في ال API
        

         

           return LoginResponse(
            success: false,
            message: (responseData['Error_Description'] ?? ' خطأ في تسجيل الدخول' ) +   ' يرجى التأكد من اسم المستخدم او كلمة المرور ' ,
          ); 
        } else {
           return LoginResponse(
            success: false,
            message: responseData['Error_Description'] ?? 'خطأ في تسجيل الدخول',
          );
        }
        } 
      }

      return LoginResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
      );
    } catch (e) {
      return LoginResponse(
        success: false,
       // message: 'خطأ في الاتصال: ${e.toString()}',
          message: 'خطأ في الاتصال',
      );
    }
  }

  // تحويل صلاحيات API إلى صلاحيات التطبيق
  static List<String> _getPermissionsFromPrivilege(Map<String, dynamic>? privilege) {
    List<String> permissions = [];

    if (privilege != null) {
      if (privilege['meterRead'] == true) {
        permissions.addAll(['view_subscribers', 'meter_reading']);
      }
      if (privilege['meterPay'] == true) {
        permissions.addAll(['view_subscribers', 'collection', 'reports']);
      }
    }

    return permissions;
  }

  // جلب المشتركين
  static Future<List<Subscriber>> getSubscribers({String? search="" , required    context }    ) async {
    print("جلب المشتركين");
    try {
      if (_authToken == null) {
        print("لا يوجد توكن مصادقة");
        return [];
      }

 


var _lastReq = ApiReqRes(
data: EncryptionService.encryptObject(({"Datatype": "1"}))
  
);

      final response = await http.post(
        Uri.parse('$baseUrl/billers/Upload_Data'),
        headers: {
          'Content-Type': 'application/json',
          'Token': _authToken!,
        },
         body: jsonEncode(_lastReq.toJson()),

       // body: jsonEncode({"Datatype": "1"}),
      ).timeout(timeoutDuration);
    //  print(response.statusCode);
     // print("محتوى الاستجابة: ${response.body}");
      if (response.statusCode == 200) {

        final responseData = EncryptionService.decryptToMap(response.body);
  print("محتوى الاستجابة: ${responseData}");
    print("${responseData}");
       // final responseData = jsonDecode(response.body);

        if (responseData!['Error_Code'] == '000') {
          final List<dynamic> dataArray = responseData['DataArray'] ?? [];

          List<Subscriber> subscribers = dataArray.map((item) {
            return Subscriber(
              id: item['partnerid'] ?? 0,
              subscriberNumber: item['fax']?.toString() ?? '',
              subscriberName: item['name'] ?? '',
              address: null,
              phone: null,
              meterNumber: item['nickname']?.toString(),
              currentBalance: (item['upper'] ?? 0).toDouble(),
              lastReading: (item['lower'] ?? 0).toDouble(),
              lastReadingDate: null,
            );
          }).toList();

          // تطبيق البحث إذا كان موجوداً
          if (search != null && search.isNotEmpty) {
            subscribers = subscribers.where((subscriber) {
              return subscriber.subscriberName.toLowerCase().contains(search.toLowerCase()) ||
                     subscriber.subscriberNumber.contains(search) ||
                     (subscriber.meterNumber?.contains(search) ?? false);
            }).toList();
          }

          return subscribers;
        }else   if (responseData['Error_Code'] == '401' || responseData['Error_Code'] == '403') {
      
  logout(context);
           return [];
        }else{

           return [];
        }
      }else{

         if (response.statusCode.toString() == "401" || response.statusCode.toString() == "403") {
      
            logout(context);

           return [];
        }
      }

      return [];
    } catch (e) {
        CustomTopErrorSnackBar(context, "خطأ في الاتصال : لا يوجد اتصال مع الخادم");
      //print("خطأ في جلب المشتركين: ${e.toString()}");
         print("خطأ في جلب المشتركين: لا يوجد اتصال مع الخادم");
      return [];
    }
  }

  static Future<void> logout(context) async {

 await StorageService.clearAll();
       
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => Directionality( textDirection: TextDirection.rtl,  child: const LoginScreen())),
          (route) => false,
        );
     
             ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('يجب عليك تسجيل الدخول'),
              backgroundColor: Colors.orange,
            ),
          );
    
  }

  // الاستعلام عن الرصيد
  static Future<BalanceInquiryResponse> inquireBalance(BalanceInquiryRequest request , context) async {
    print("الاستعلام عن الرصيد");
    try {
      if (_authToken == null) {
        print('لا يوجد توكن مصادقة');

        return BalanceInquiryResponse(
          success: false,
          message: 'لا يوجد توكن مصادقة',
          currentBalance: 0,
          lastReading: 0,
          estimatedBill: 0,
                     AllowPart: 'Y',
                Lower: 0,
        );
      }
var _lastReq = ApiReqRes(
data: EncryptionService.encryptObject(({"Billing_No": request.subscriberNumber}))
  
);


      final response = await http.post(
        Uri.parse('$baseUrl/billers/Biller_Bill_Inquiry'),
        headers: {
          'Content-Type': 'application/json',
          'Token': _authToken!,
        },
         body: jsonEncode(_lastReq.toJson()),

       // body: jsonEncode({"Billing_No": request.subscriberNumber}),
      ).timeout(timeoutDuration);
print(response.statusCode);
print(response.body);
      if (response.statusCode == 200) {

         final responseData = EncryptionService.decryptToMap(response.body);
print(responseData);
       // final responseData = jsonDecode(response.body);

        if (responseData!['Error_Code'] == '000') {
          final List<dynamic> billingsRec = responseData['BillingsRec'] ?? [];

          if (billingsRec.isNotEmpty) {
            final billData = billingsRec.first;





/*
{
    "Error_Code": "000",
    "Error_Description": "Operation is Success",
    "BillingsRec": [
        {
            "SubscribeName": "منشار البشيري - صلاح حسين محمد البشيري - 00002243 ",
           	     "Billing_No": "1436179",
            "Bill_No": "1692463",
            "LastReadingMeter": "95721.0",
            "BillStatus": "BillNew",
            "Due_Amount": "350713.5",
            "Data_balance": "",
            "Package_Balance": 0,
            "Package_Id": "",
            "Issue_Date": "2024-03-28",
            "Open_Date": "2025-08-05T16:11:12.779Z",
            "Due_Date": "2025-08-05T16:11:12.779Z",
            "Expiry_Date": "2024-04-14",
            "Close_Date": null,
            "Service_Type": 228,
            "Bill_Type": "Recurring",
            "Allow_Part": "Y",
            "Lower": "0.0",
            "Upper": "350713.5",
            "Allow_Over": "Y",
            "Message": "مبلغ الفاتوره الحالية:0.0 متأخرات:350713.5 عليكم:350713.5"
        }
    ]
}

*/
            return BalanceInquiryResponse(
              success: true,
              message: billData['Message'] ?? responseData['Error_Description'] ?? 'تم الاستعلام بنجاح',
              currentBalance: double.tryParse(billData['Due_Amount']?.toString() ?? '0') ?? 0,
              lastReading: double.tryParse(billData['LastReadingMeter']?.toString() ?? '0') ?? 0,
              estimatedBill: double.tryParse(billData['Lower']?.toString() ?? '0') ?? 0,
              subscriberName: billData['SubscribeName'],
              billNumber: billData['Bill_No']?.toString(),
              billStatus: billData['BillStatus'],
              issueDate: billData['Issue_Date'],
              dueDate: billData['Due_Date'],
               AllowPart: billData['Allow_Part'],
                Lower: double.tryParse(billData['Lower']?.toString() ?? '0') ?? 0,  
            );
          }
        } 
   if (responseData!['Error_Code'] == '401' || responseData!['Error_Code'] == '403') {
      
            logout(context);

             return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
                   AllowPart: 'Y',
                Lower: 0,
      );
           }

        return BalanceInquiryResponse(
          success: false,
          message: responseData['Error_Description'] ?? 'لم يتم العثور على بيانات',
          currentBalance: 0,
          lastReading: 0,
          estimatedBill: 0,
                    AllowPart: 'Y',
                Lower: 0,
        );
      }else{

         if (response.statusCode.toString() == "401" || response.statusCode.toString() == "403") {
      
            logout(context);

            return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
                   AllowPart: 'Y',
                Lower: 0,
      );
        }else{

            return BalanceInquiryResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
                   AllowPart: 'Y',
                Lower: 0,
      );

        }
      }

    
    } catch (e) {
      return BalanceInquiryResponse(
        success: false,
      //  message: 'خطأ في الاتصال: ${e.toString()}',
        message: 'خطأ في الاتصال: لا يوجد اتصال مع الخادم',
        currentBalance: 0,
        lastReading: 0,
        estimatedBill: 0,
                   AllowPart: 'Y',
                Lower: 0,
      );
    }
  }

  // معالجة التحصيل
  static Future<CollectionResponse> processCollection(CollectionRequest request , context) async {
    print("معالجة التحصيل");
    try {
      if (_authToken == null) {
        return CollectionResponse(
          success: false,
          message: 'لا يوجد توكن مصادقة',
        );
      }

 print("البيانات المرسلة: ${jsonEncode(request.toJson())}");
  print("البيانات المرسلة: ${_authToken}");

  var _lastReq = ApiReqRes(
data: EncryptionService.encryptObject(request.toJson())
  
);


      final response = await http.post(
        Uri.parse('$baseUrl/billers/Biller_Bill_Payment'),
        headers: {
          'Content-Type': 'application/json',
          'Token': _authToken!,
        },
         body: jsonEncode(_lastReq.toJson()),

       // body: jsonEncode(request.toJson()),
      ).timeout(timeoutDuration);


      print("رمز الاستجابة: ${response.statusCode}");
      print("محتوى الاستجابة: ${response.body}");


      if (response.statusCode == 200) {

        final responseData = EncryptionService.decryptToMap(response.body);
  print("محتوى الاستجابة: ${responseData}");
       // final responseData = jsonDecode(response.body);

        if (responseData!['Error_Code'] == '000') {
          return CollectionResponse(
            success: true,
            message: responseData['Error_Description'] ?? 'تم التحصيل بنجاح',
          //  docId:   responseData['recieveNo'] ?? '0',
             docId:   responseData['display_name'] ?? '0',
            transactionDate: DateTime.now() ,
          );
        } else {

           if (responseData!['Error_Code'] == '401'  || responseData!['Error_Code'] == '403') {
      
            logout(context);

               return CollectionResponse(
            success: false,
            message: responseData['Error_Description'] ?? 'خطأ في معالجة التحصيل',
          );
           }

          return CollectionResponse(
            success: false,
            message: responseData['Error_Description'] ?? 'خطأ في معالجة التحصيل',
          );
        }
      }else{

         if (response.statusCode.toString() == "401" || response.statusCode.toString() == "403") {
      
            logout(context);

             return CollectionResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
      );
        }else{

             return CollectionResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
      );

        }
      }


    
    } catch (e) {
      return CollectionResponse(
        success: false,
       // message: 'خطأ في الاتصال: ${e.toString()}',
          message: 'خطأ في الاتصال: لا يوجد اتصال مع الخادم',
      );
    }
  }

  // دالة مساعدة للاستعلام عن الرصيد (نفس inquireBalance)
  static Future<BalanceInquiryResponse> getBalance(BalanceInquiryRequest request , context) async {
    return inquireBalance(request , context);
  }

  // البحث عن عداد (يستخدم البيانات المحلية)
  static Future<MeterLookupResponse> lookupMeter(MeterLookupRequest request , context) async {
    print("البحث عن عداد");
    try {
      // البحث في البيانات المحلية للمشتركين
      final subscribers = await getSubscribers(context: context);

      final subscriber = subscribers.firstWhere(
        (s) => s.meterNumber == request.meterNumber || s.subscriberNumber == request.meterNumber,
        orElse: () => throw Exception('لم يتم العثور على العداد'),
      );

      return MeterLookupResponse(
        success: true,
        message: 'تم العثور على العداد',
        subscriber: subscriber,
        lastReading: subscriber.lastReading,
        lastReadingDate: subscriber.lastReadingDate,
      );
    } catch (e) {
      return MeterLookupResponse(
        success: false,
        message: 'لم يتم العثور على العداد: ${e.toString()}',
      );
    }
  }

  // معالجة قراءة العداد (حفظ محلي)
  static Future<MeterReadingResponse> processMeterReading(MeterReadingRequest request) async {
    print("معالجة قراءة العداد");
    try {
      // حساب الاستهلاك
      final consumption = request.previousReading != null
          ? request.currentReading - request.previousReading!
          : 0.0;

      // تقدير الفاتورة (يمكن تخصيص هذا حسب التعرفة)
      final estimatedBill = consumption * 0.5; // مثال: 0.5 ريال لكل وحدة

      // في التطبيق الحقيقي، يجب حفظ هذه البيانات في قاعدة البيانات المحلية
      // هنا سنرجع استجابة نجاح

      return MeterReadingResponse(
        success: true,
        message: 'تم حفظ قراءة العداد بنجاح',
        readingId: DateTime.now().millisecondsSinceEpoch,
        previousReading: request.previousReading,
        currentReading: request.currentReading,
        consumption: consumption,
        estimatedBill: estimatedBill,
        readingDate: request.readingDate,
      );
    } catch (e) {
      return MeterReadingResponse(
        success: false,
        message: 'خطأ في معالجة قراءة العداد: ${e.toString()}',
      );
    }
  }

  // Get collection reports (البيانات المحلية)
  static Future<CollectionReportResponse> getCollectionReports( CollectionReportRequest request , context ) async {


  




    
       print("معالجة التحصيل");
    try {
      if (_authToken == null) {
        return CollectionReportResponse(
          success: false,
          message: 'لا يوجد توكن مصادقة',
          collections: []

        );
      }

       print(request.toJson());

var _lastReq = ApiReqRes(
data: EncryptionService.encryptObject(request.toJson())
  
);
      final response = await http.post(
      request.Billing_No == "" ?   Uri.parse('$baseUrl/billers/userbilling'): Uri.parse('$baseUrl/billers/PartnerbyUser'),
      
        headers: {
          'Content-Type': 'application/json',
          'Token': _authToken!,
        },
         body: jsonEncode(_lastReq.toJson()),

       // body: jsonEncode(request.toJson()),
      ).timeout(timeoutDuration);


    print(response.statusCode);
      print("محتوى الاستجابة: ${response.body}");


      if (response.statusCode == 200) {

         final responseData = EncryptionService.decryptToMap(response.body);
print("محتوى الاستجابة: ${responseData}");
        //final responseData = jsonDecode(response.body);

        if (responseData!['Error_Code'] == '000') {


          final List<dynamic> dataArray = responseData['BillingsRec'] ?? [];
 print(dataArray.length);
          List<BillingsRec> billingsRecs = dataArray.map((json) {
            return BillingsRec(


 /*  createUid: json["create_uid"] == null ? [] : List<dynamic>.from(json["create_uid"]!.map((x) => x)),
            refundInvoiceId: json["refund_invoice_id"],
            userId: json["user_id"] == null ? [] : List<dynamic>.from(json["user_id"]!.map((x) => x)),
            xAmountTotal: double.parse(json["x_amount_total"].toString())  ,
            xDateEnd: DateTime.tryParse(json["x_date_end"] ?? ""),
            xNut1: json["x_nut1"] == null ? [] : List<dynamic>.from(json["x_nut1"]!.map((x) => x)),
            dateInvoice: DateTime.tryParse(json["date_invoice"] ?? ""),
            previousInvoiceId: json["previous_invoice_id"],
            xNumber: json["x_number"],
            displayName: json["display_name"],
            writeUid: json["write_uid"] == null ? [] : List<dynamic>.from(json["write_uid"]!.map((x) => x)),
            partnerId: json["partner_id"] == null ? [] : List<dynamic>.from(json["partner_id"]!.map((x) => x)),
            id: json["id"],
            moveId: json["move_id"] == null ? [] : List<dynamic>.from(json["move_id"]!.map((x) => x)),
            amountTotal:  double.parse(json["amount_total"].toString())    ,*/

                     createUid: json["create_uid"] == null ? [] : List<dynamic>.from(json["create_uid"]!.map((x) => x)),
           // refundInvoiceId: json["refund_invoice_id"],
           userId: json["write_uid"] == null ? [] : List<dynamic>.from(json["write_uid"]!.map((x) => x)),
            xAmountTotal: double.parse(json["x_invoice_amount"].toString())   ,
           // xDateEnd: DateTime.tryParse(json["x_date_end"] ?? ""),
            xNut1: json["x_nut1"] == null ? [] : List<dynamic>.from(json["x_nut1"]!.map((x) => x)),
         dateInvoice: DateTime.tryParse(json["payment_date"] ?? ""),
           // previousInvoiceId: json["previous_invoice_id"],
            xNumber: json["id"].toString().toString(),
            displayName: json["display_name"],
            writeUid: json["write_uid"] == null ? [] : List<dynamic>.from(json["write_uid"]!.map((x) => x)),
            partnerId: json["partner_id"] == null ? [] : List<dynamic>.from(json["partner_id"]!.map((x) => x)),
            id: json["display_name"] ,//json["id"].toString(),
             paymentType: json["payment_type"].toString(),
            //moveId: json["move_id"] == null ? [] : List<dynamic>.from(json["move_id"]!.map((x) => x)),
            amountTotal:  double.parse(json["amount"].toString()) ,

 
            );
          }).toList();

     int totalTransactions=0;
    double totalAmountDue=0;
    double totalAmountPaid=0;
    double totalOutstanding=0;
  

billingsRecs.forEach((billingsRec) {
  totalTransactions=totalTransactions+1;
  totalAmountDue=totalAmountDue + billingsRec.xAmountTotal! ;
  totalAmountPaid=totalAmountPaid + billingsRec.amountTotal! ;
  totalOutstanding=totalOutstanding + (billingsRec.xAmountTotal! - billingsRec.amountTotal!) ;

});
  Map<String, int> paymentMethodCounts = Map<String, int>.from(  {"Cash" : totalTransactions }) ;
    Map<String, double> paymentMethodAmounts = Map<String, double>.from(  {"Cash" : totalAmountPaid }) ; 


var collectionReportSummary = CollectionReportSummary(
  paymentMethodAmounts: paymentMethodAmounts,
  paymentMethodCounts: paymentMethodCounts ,
  totalAmountDue: totalAmountDue ,
  totalAmountPaid: totalAmountPaid ,
  totalOutstanding: totalOutstanding ,
  totalTransactions:totalTransactions );


          


          return CollectionReportResponse(
            success: true,
            message: responseData['Error_Description'] ?? 'تم جلب التقارير بنجاح',
      collections:billingsRecs, // قائمة فارغة حالياً
      summary: collectionReportSummary

          );
        } else {

                 if (responseData!['Error_Code'] == '401' || responseData!['Error_Code'] == '403' ) {
      
            logout(context);

      return CollectionReportResponse(
            success: false,
            message: responseData['Error_Description'] ?? 'خطأ في جلب تقارير التحصيلات',
               collections: [], // قائمة فارغة حالياً
          );
           }


          return CollectionReportResponse(
            success: false,
            message: responseData['Error_Description'] ?? 'خطأ في جلب تقارير التحصيلات',
               collections: [], // قائمة فارغة حالياً
          );
        }
      }else{

         if (response.statusCode.toString() == "401" || response.statusCode.toString() == "403") {
      
            logout(context);

             return CollectionReportResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
              collections: [], // قائمة فارغة حالياً
      );
        }else{

             return CollectionReportResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
          collections: [], // قائمة فارغة حالياً
      );

        }
      }


    
    } catch (e) {
      print(e.toString());
      return CollectionReportResponse(
        success: false,
        //message: 'خطأ في الاتصال: ${e.toString()}',
        message: 'خطأ في الاتصال: لا يوجد اتصال مع الخادم',
          collections: [], // قائمة فارغة حالياً
      );
    }




  }



    static Future<ChangePasswordResponse> changePassword(ChangePasswordRequest request , context) async {
    print("تغيير كلمة المرور");
    try {
      if (_authToken == null) {
        return ChangePasswordResponse(
          success: false,
          message: 'لا يوجد توكن مصادقة',
          errorCode: '9999',
        );
      }

      print("البيانات المرسلة: ${jsonEncode(request.toJson())}");
      print("الرابط: $baseUrl/auth/ChangePassword");


var _lastReq = ApiReqRes(
data: EncryptionService.encryptObject(request.toJson())
  
);

      final response = await http.post(
        Uri.parse('$baseUrl/auth/ChangePassword'),
        headers: {
          'Content-Type': 'application/json',
          'token': _authToken!,
        },
         body: jsonEncode(_lastReq.toJson()),
      //  body: jsonEncode(request.toJson()),
      ).timeout(timeoutDuration);

      print("رمز الاستجابة: ${response.statusCode}");
      print("محتوى الاستجابة: ${response.body}");

      if (response.statusCode == 200) {
         final responseData = EncryptionService.decryptToMap(response.body);

      //  final responseData = jsonDecode(response.body);
        return ChangePasswordResponse.fromJson(responseData!);
      }
              if (response.statusCode.toString() == "401") {
      
            logout(context);

      return ChangePasswordResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
        errorCode: '9998',
      );
           }
      return ChangePasswordResponse(
        success: false,
        message: 'خطأ في الاتصال بالخادم',
        errorCode: '9998',
      );
    } catch (e) {
      return ChangePasswordResponse(
        success: false,
        message: 'خطأ في الاتصال: ${e.toString()}',
        errorCode: '9997',
      );
    }
  }
}
