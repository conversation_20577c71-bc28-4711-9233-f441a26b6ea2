import 'package:elecollect_app/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:ai_barcode_scanner/ai_barcode_scanner.dart';

class QRScannerScreen extends StatefulWidget {
  const QRScannerScreen({super.key});

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> {
  String? scannedValue;
  final TextEditingController _manualController = TextEditingController();
  bool _isScanning = true;
  bool _hasScanned = false;

  @override
  void initState() {
    super.initState();
    // Reset scanning state when screen is initialized
    _isScanning = true;
    _hasScanned = false;
  }

  @override
  void dispose() {
    _manualController.dispose();
    super.dispose();
  }

  void _handleScanResult(String value) {
    if (_hasScanned) return; // Prevent multiple scans

    setState(() {
      _hasScanned = true;
      _isScanning = false;
      scannedValue = value;
    });

    // Add a small delay to ensure the scanner is properly disposed
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        Navigator.of(context).pop(value);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مسح الباركود/QR'),
        backgroundColor: AppTheme.themeData.primaryColor ,//Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Instructions
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: Colors.blue.shade50,
            child: const Column(
              children: [
                Icon(
                  Icons.qr_code_scanner,
                  size: 48,
                  color: Colors.blue,
                ),
                SizedBox(height: 8),
                Text(
                  'وجه الكاميرا نحو الباركود أو QR Code',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 4),
                Text(
                  'سيتم إرجاع النتيجة تلقائياً عند المسح',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          // Scanner
          Expanded(
            child: _isScanning ? AiBarcodeScanner(
              onDetect: (BarcodeCapture capture) {
                final String? scannedValue = capture.barcodes.first.rawValue;
                if (scannedValue != null && scannedValue.isNotEmpty && !_hasScanned) {
                  _handleScanResult(scannedValue);
                }
              },
              onDispose: () {
                debugPrint("QR Scanner disposed");
              },
            ) : Container(
              color: Colors.black,
              child: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            ),
          ),
          
          // Manual input option
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Divider(),
                const Text(
                  'أو أدخل الرقم يدوياً:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _manualController,
                        decoration: const InputDecoration(
                          hintText: 'أدخل رقم العداد',
                          border: OutlineInputBorder(),
                        ),
                        onSubmitted: (value) {
                          if (value.trim().isNotEmpty && !_hasScanned) {
                            _handleScanResult(value.trim());
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _hasScanned ? null : () {
                        final value = _manualController.text.trim();
                        if (value.isNotEmpty) {
                          _handleScanResult(value);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('تأكيد'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
