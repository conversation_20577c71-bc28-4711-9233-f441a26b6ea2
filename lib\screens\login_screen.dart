import 'package:elecollect_app/theme/app_theme.dart';
import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../models/user.dart';
import '../theme/app_colors.dart';
import '../theme/app_styles.dart';
import '../theme/app_icons.dart';
// import 'home_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _loadSavedUsername();
  }

  Future<void> _loadSavedUsername() async {
    final savedUsername = await StorageService.getSavedUsername();
    if (savedUsername != null) {
      _usernameController.text = savedUsername;
      setState(() {
        _rememberMe = true;
      });
    }
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Login attempt with encryption

      final loginRequest = LoginRequest(
        username: _usernameController.text.trim(),
        password: _passwordController.text,
      );

      final response = await ApiService.login(loginRequest);
print(response.success);
print(response.user);

      if (response.success && response.user != null) {
        // حفظ بيانات المستخدم
        await StorageService.saveUser(response.user!);

        // حفظ التوكن
        final token = ApiService.getAuthToken();
        if (token != null) {
          await StorageService.saveAuthToken(token);
        }

        // حفظ اسم المستخدم إذا كان مطلوباً
        await StorageService.saveUsername(_usernameController.text.trim(), _rememberMe);

        if (mounted) {
          // الانتقال للشاشة الرئيسية
          Navigator.pushReplacementNamed(context, '/home');
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message.isEmpty ? 'Login failed' : response.message),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        // Connection error: ${e.toString()}
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
                AppTheme.themeData.primaryColor ,//AppColors.primary,

            
                AppTheme.themeData.primaryColorLight//AppColors.primaryLight,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Card(
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Container(
                  padding: const EdgeInsets.all(32.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: AppColors.cardBackground,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadowMedium,
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        
                           Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: Image(
                    height:   90,
                    width:   90,
                    fit: BoxFit.fill,
                    image: const AssetImage('assets/images/peclogo.png')),
              ),
                  Text(
                              'المؤسسة العامة للكهرباء',
                              style: AppStyles.bodyMedium.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                             const SizedBox(height: 32),
                          Row(
                                                crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
                          children: [
                        
                                 Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color:   AppTheme.themeData.primaryColor,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: const Icon(
                            Icons.electrical_services, 
                            color: AppColors.textOnDark,
                            size: 40,
                          ),
                        ),
                         const SizedBox(width: 8),
                           Text(
                          'PEC',
                          style: AppStyles.headingLarge.copyWith(
                            color:   AppTheme.themeData.primaryColor,
                          ),
                        ),
                          ],
                        ),
                       
                        // Logo
                  
                        const SizedBox(height: 24),
                        
                        // Title
                     
                       
                      
                        
                        // Username Field
                        TextFormField(
                          controller: _usernameController,
                          decoration: InputDecoration(
                            labelText: 'اسم المستخدم',
                            prefixIcon: const Icon(Icons.person),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: AppColors.sectionBackground,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'ادخل اسم المستخدم';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        
                        // Password Field
                        TextFormField(
                          controller: _passwordController,
                          obscureText: true,
                          decoration: InputDecoration(
                            labelText: 'كلمة المرور',
                            prefixIcon: const Icon(Icons.lock),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: AppColors.sectionBackground,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'ادخل كلمة المرور';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        
                        // Remember Me Checkbox
                        Row(
                          children: [
                            Checkbox(
                              value: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value ?? false;
                                });
                              },
                            ),
                            const Text('تذكر'),
                          ],
                        ),
                        const SizedBox(height: 24),
                        
                        // Login Button
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _login,
                            style:   AppTheme.themeData.elevatedButtonTheme.style , //  AppStyles.primaryButton,
                            child: _isLoading
                                ? const CircularProgressIndicator(
                                    color: AppColors.textOnDark,
                                  )
                                : const Text(
                                    'دخول',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Version Info
                        Text(
                          'Version 1.0.0',
                          style: AppStyles.bodySmall.copyWith(
                            color: AppColors.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
