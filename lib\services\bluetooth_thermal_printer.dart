import 'dart:convert' show utf8;  
import 'dart:io';
import 'dart:typed_data';

import 'package:elecollect_app/models/invoice_receipt.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:printing/printing.dart';
import 'package:image/image.dart' as img;
import '../models/collection.dart';
import '../models/collection_report.dart';
import '../models/user.dart';
import 'windows_system_printer.dart';

class BluetoothThermalPrinter {
  static BluetoothInfo? _selectedPrinter;
  static bool _isConnected = false;

  // Request Bluetooth permissions
  static Future<bool> _requestBluetoothPermissions() async {
    try {
      Map<Permission, PermissionStatus> statuses = await [
        Permission.bluetooth,
        Permission.bluetoothConnect,
        Permission.bluetoothScan,
        Permission.location,
      ].request();

      return statuses.values.every((status) => status.isGranted);
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting permissions: $e');
      }
      return false;
    }
  }

  // Get available Bluetooth printers
  static Future<List<BluetoothInfo>> getAvailablePrinters() async {
    try {
      if (kDebugMode) {
        print('Getting available printers...');
        print('Platform: ${Platform.operatingSystem}');
      }

      // Windows-specific handling - use system printers
      if (Platform.isWindows) {
        return await _getWindowsSystemPrinters();
      }

      // Request permissions first (Android/iOS)
      bool permissionsGranted = await _requestBluetoothPermissions();
      if (!permissionsGranted) {
        throw Exception('يرجى منح صلاحيات البلوتوث للتطبيق');
      }

      // Get paired devices
      final List<BluetoothInfo> devices = await PrintBluetoothThermal.pairedBluetooths;
      if (kDebugMode) {
        print('Found ${devices.length} paired devices');
      }
      return devices;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting printers: $e');
      }
      throw Exception('خطأ في البحث عن الطابعات: ${e.toString()}');
    }
  }

  // Windows-specific printer discovery using system printers
  static Future<List<BluetoothInfo>> _getWindowsSystemPrinters() async {
    try {
      if (kDebugMode) {
        print('Getting Windows system printers...');
      }

      // Get system printers for Windows
      List<String> printers = await WindowsSystemPrinter.getAvailablePrinters();

      // Convert system printer names to BluetoothInfo format for compatibility
      List<BluetoothInfo> devices = printers.map((printerName) {
        return BluetoothInfo(
          name: printerName,
          macAdress: 'SYSTEM_PRINTER', // Special identifier for system printers
        );
      }).toList();

      if (kDebugMode) {
        print('Found ${devices.length} system printers on Windows');
        for (var device in devices) {
          print('Printer: ${device.name}');
        }
      }

      return devices;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting Windows system printers: $e');
      }
      return [];
    }
  }

  // Connect to a specific printer
  static Future<bool> connectToPrinter(BluetoothInfo printer) async {
    try {
      _selectedPrinter = printer;

      if (kDebugMode) {
        print('Attempting to connect to printer: ${printer.name}');
        print('Platform: ${Platform.operatingSystem}');
        print('MAC Address: ${printer.macAdress}');
      }

      // Windows-specific handling - use system printer
      if (Platform.isWindows) {
        if (kDebugMode) {
          print('Windows platform detected - using system printer');
        }

        return await _connectToWindowsSystemPrinter(printer);
      }

  
      // Try to connect (Android/iOS)
      final bool result = await PrintBluetoothThermal.connect(macPrinterAddress: printer.macAdress);

      _isConnected = result;
      if (_isConnected) {
        if (kDebugMode) {
          print('Successfully connected to printer');
        }
        // Initialize MHT-P29L printer for Arabic
        await _initializeMHTP29LForArabic();
      } else {
        _selectedPrinter = null;
        if (kDebugMode) {
          print('Failed to connect to printer');
        }
      }
      return _isConnected;
    } catch (e) {
      _isConnected = false;
      _selectedPrinter = null;
      if (kDebugMode) {
        print('Error connecting to printer: $e');
      }
      throw Exception('خطأ في الاتصال بالطابعة: ${e.toString()}');
    }
  }

  // Windows-specific printer connection using system printer
  static Future<bool> _connectToWindowsSystemPrinter(BluetoothInfo printer) async {
    try {
      if (kDebugMode) {
        print('Windows platform detected - using system printer');
        print('Target printer: ${printer.name}');
      }

      // Check if this is a system printer (identified by special MAC address)
      if (printer.macAdress != 'SYSTEM_PRINTER') {
        if (kDebugMode) {
          print('Error: Not a valid system printer');
        }
        return false;
      }

      // Connect to the Windows system printer
      bool connected = await WindowsSystemPrinter.connectToPrinter(printer.name);

      if (connected) {
        _isConnected = true;
        _selectedPrinter = printer;

        if (kDebugMode) {
          print('Successfully connected to Windows system printer: ${printer.name}');
        }

        return true;
      } else {
        _isConnected = false;
        _selectedPrinter = null;
        if (kDebugMode) {
          print('Failed to connect to Windows system printer: ${printer.name}');
        }
        return false;
      }
    } catch (e) {
      _isConnected = false;
      _selectedPrinter = null;
      if (kDebugMode) {
        print('Windows system printer connection error: $e');
      }
      return false;
    }
  }



  // Initialize MHT-P29L printer specifically for Arabic text with GB18030 support
  static Future<void> _initializeMHTP29LForArabic() async {
    try {
      List<int> initBytes = [];

      // Reset printer completely
      initBytes.addAll([27, 64]); // ESC @ - Initialize

      // Disable GB18030 and set to international mode
      initBytes.addAll([28, 46]); // FS . - Cancel GB18030 mode
      initBytes.addAll([27, 82, 0]); // ESC R 0 - International character set

      // Set UTF-8 encoding mode
      initBytes.addAll([27, 116, 3]); // ESC t 3 - UTF-8 codepage

      // Enable Arabic support
      initBytes.addAll([28, 67, 2]); // FS C 2 - Arabic/Hebrew support

      // Send initialization commands
      await PrintBluetoothThermal.writeBytes(initBytes);

      // Wait for printer to process
      await Future.delayed(const Duration(milliseconds: 1000));

    } catch (e) {
      if (kDebugMode) {
        print('Error initializing MHT-P29L for Arabic: $e');
      }
    }
  }



  // Disconnect from printer
  static Future<void> disconnect() async {
    try {
      if (Platform.isWindows) {
        await WindowsSystemPrinter.disconnect();
      } else {
        await PrintBluetoothThermal.disconnect;
      }
      _isConnected = false;
      _selectedPrinter = null;
    } catch (e) {
      throw Exception('خطأ في قطع الاتصال: ${e.toString()}');
    }
  }

  // Check if connected
  static bool isConnected() {
    if (Platform.isWindows) {
      return WindowsSystemPrinter.isConnected();
    }
    return _isConnected && _selectedPrinter != null;
  }

  // Print collection receipt with Arabic support using ESC/POS
  static Future<void> printCollectionReceipt({
    required InvoiceReceipt invoiceReceipt 
    // required Subscriber subscriber,
  //  required CollectionResponse collectionResponse,
   // required double amountPaid,
  //  required String paymentMethod,
   // String? notes,
  }) async {
    try {
      // Check platform and route to appropriate printer
      if (Platform.isWindows) {
        // Use Windows system printer
        await _printToWindowsSystemPrinter(
          invoiceReceipt: invoiceReceipt

         // subscriber: subscriber,
         // collectionResponse: collectionResponse,
         // amountPaid: amountPaid,
         // paymentMethod: paymentMethod,
         // notes: notes,
        );
        return;
      }

      // For Android/iOS - use simple text-based thermal printing
      if (!isConnected()) {
        throw Exception('الطابعة غير متصلة. يرجى الاتصال بالطابعة أولاً.');
      }

      
      final List<int> bytes = await _generateSimpleReceipt(
         invoiceReceipt: invoiceReceipt,
       // subscriber: subscriber,
       // collectionResponse: collectionResponse,
       // amountPaid: amountPaid,
       // paymentMethod: paymentMethod,
       // notes: notes,
      );


    /*  final List<int> bytes = await printCollectionReceiptForAndroid(
          invoiceReceipt,
       // subscriber: subscriber,
       // collectionResponse: collectionResponse,
       // amountPaid: amountPaid,
       // paymentMethod: paymentMethod,
       // notes: notes,
      );
*/



       
      final bool result = await PrintBluetoothThermal.writeBytes(bytes);

      if (!result) {
        throw Exception('فشل في الطباعة');
      }

    } catch (e) {
      throw Exception('خطأ في الطباعة: ${e.toString()}');
    }
  }

 
   static Future<List<int>> printCollectionReceiptForAndroid( 
         InvoiceReceipt invoiceReceipt
  //  CollectionReport collection,
   //  User user
     
     ) async {
    try {

        await _loadArabicFonts();


 

      if (kDebugMode) {
        print('Creating collection receipt for Windows system printer...');
      }

      // Create a PDF document for the collection receipt with thermal printer format
      final pdf = pw.Document();

      // Use thermal printer paper size (80mm width)
    /*  const thermalFormat = PdfPageFormat(
        80 * PdfPageFormat.mm, // 80mm width
        double.infinity, // Auto height
        marginAll: 2 * PdfPageFormat.mm, // Small margins like thermal printer
      );
*/

        const thermalFormat = PdfPageFormat(
      75 * PdfPageFormat.mm, // 72mm width (optimized for safe printing)
     // 120 * PdfPageFormat.mm, // Fixed height for 80mm paper
        double.infinity, // Auto height
      marginAll: 1 * PdfPageFormat.mm, // Increased margins for 72mm width
    );

      pdf.addPage(
        pw.Page(
          pageFormat: thermalFormat,
              margin: const pw.EdgeInsets.all(10), // Increased margin for 72mm width

          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header - Center aligned like thermal printer
                pw.Center(
                  child: pw.Column(
                    children: [

                        // Logo at the top
                 if (_logoImage != null)
                  pw.Container(
                  height: 80, // Smaller logo for minimal receipt
                  child: pw.Image(_logoImage!),
                  ),
                     if (_logoImage != null) pw.SizedBox(height: 2),


                      pw.Text(
                        'سند تحصيل',
                    style: _createTextStyleWithFallback(fontSize: 16, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                      ),
                      pw.SizedBox(height: 2),
                      pw.Text(
                        'المؤسسة العامة للكهرباء',
                          style: _createTextStyleWithFallback(fontSize: 10, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                      ),
                      pw.Text(
                        '================================',
                        style: pw.TextStyle(fontSize: 8),
                      ),
                    ],
                  ),
                ),
                pw.SizedBox(height: 4), 

                // Transaction details - Left aligned like thermal printer
               _buildMinimalRow('رقم السند:', ' ${invoiceReceipt.docId!.isNotEmpty ? invoiceReceipt.docId : 'غير محدد'}'),
   _buildMinimalRow('تاريخ التحصيل :', '${_formatDateTime(invoiceReceipt.dateInvoice)}'),
      _buildMinimalRow('تاريخ الطباعة :', '${_formatDateTime(invoiceReceipt.datePrint)}'),
    //  _buildMinimalRow('الوقت:', '${_formatTime(collection.transactionDate)} '),

            /*    pw.Text(
                  'رقم السند: ${collection.transactionId.isNotEmpty ? collection.transactionId : 'غير محدد'}',
                       style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'التاريخ: ${_formatDateTime(collection.transactionDate)}',
                    style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الوقت: ${_formatTime(collection.transactionDate)}',
                 style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),*/
                pw.SizedBox(height: 2),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.SizedBox(height: 2),

   _buildMinimalRow('رقم المشترك:', '${invoiceReceipt.subscriberNumber}' ),
      _buildMinimalRow('اسم المشترك:', '${invoiceReceipt.subscriberName}'),
      //   _buildMinimalRow('رقم العداد:', '${invoiceReceipt.meterNumber!}'),

                // Subscriber details
              /*  pw.Text(
                  'رقم الاشتراك: ${collection.subscriberNumber}',
                 style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'اسم المشترك: ${collection.subscriberName}',
                     style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                if (collection.meterNumber != null && collection.meterNumber!.isNotEmpty)
                  pw.Text(
                    'رقم العداد: ${collection.meterNumber!}',
                       style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                  ),*/

                pw.SizedBox(height: 2),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.SizedBox(height: 2),

   _buildMinimalRow('المبلغ المستحق:', '${invoiceReceipt.dueAmount!.toStringAsFixed(2)} ر.ي'),
      _buildMinimalRow('طريقة الدفع:', '${_getPaymentMethodArabic(invoiceReceipt.paymentType)}'),
         _buildMinimalRow('اسم المحصل :', ' ${invoiceReceipt.collector!} '),
                // Payment details
               /* pw.Text(
                  'المبلغ المستحق: ${collection.amountDue.toStringAsFixed(2)} ر.ي',
             style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),*/
                // Amount paid - Center aligned and bold like thermal printer
                pw.Center(
                  child: pw.Text(
                    'المبلغ المدفوع: ${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي',
                style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                  ),
                ),
              /*  pw.Text(
                  'طريقة الدفع: ${_getPaymentMethodArabic(collection.paymentMethod)}',
                        style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الرصيد الجديد: ${collection.newBalance.toStringAsFixed(2)} ر.ي',
                         style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
*/
                // Notes if available
               /* if (invoiceReceipt.notes != null && invoiceReceipt.notes!.isNotEmpty) ...[
                  pw.SizedBox(height: 2),
                  pw.Text(
                    'ملاحظات: ${invoiceReceipt.notes!}',
                  style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                  ),
                ],*/

                // Footer
                pw.SizedBox(height: 4),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.Center(
                  child: pw.Text(
                    'شكراً لكم',
                    style: _createTextStyleWithFallback(fontSize: 10, isBold: true),
              textAlign: pw.TextAlign.left,
              textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.SizedBox(height: 6), // Extra space like thermal printer
              ],
            );
          },
        ),
      );

      // Print the document
     //final result = await pdf.save();
      final Uint8List pdfUint8List = await pdf.save();
        final List<int> result = pdfUint8List.toList();
   /*  Printing.layoutPdf(
        printer: Printer(url: _selectedPrinterName!),
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );  */
 

      if (kDebugMode) {
     //   print('Windows system printer collection receipt result: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error printing collection receipt on Windows system printer: $e');
      }
      return [];
    }
  }


   // Minimal row for direct printing (72mm - optimized for safe printing)
  static pw.Widget _buildMinimalRow(String label, String value, {bool isBold = false}) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 1),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              value,
              style: _createTextStyleWithFallback(fontSize: 9, isBold: isBold),
              textAlign: pw.TextAlign.left,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              label,
              style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }


 // Generate Arabic receipt using ESC/POS with proper encoding
  static Future<List<int>> _generateSimpleReceipt({
       required  InvoiceReceipt invoiceReceipt
  //  required Subscriber subscriber,
  //  required CollectionResponse collectionResponse,
  //  required double amountPaid,
  //  required String paymentMethod,
  //  String? notes,
  }) async {
   /* try {
      
      
      // Create ESC/POS generator with Arabic support
      final profile = await CapabilityProfile.load();
      final generator = Generator(PaperSize.mm80, profile);
      List<int> bytes = [];

      // Initialize printer
      bytes += generator.reset();

      // Header with Arabic text
      bytes += generator.text('سند تحصيل',
          styles: const PosStyles(
            align: PosAlign.center,
            height: PosTextSize.size2,
            width: PosTextSize.size2,
            bold: true,
          ));
      bytes += generator.text('المؤسسة العامة للكهرباء',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.hr();

      // Transaction details in Arabic
      bytes += generator.text('رقم المعاملة: ${collectionResponse.transactionId ?? 'غير محدد'}');
      bytes += generator.text('التاريخ: ${_formatDateTime(collectionResponse.transactionDate ?? DateTime.now())}');
      bytes += generator.text('الوقت: ${_formatTime(collectionResponse.transactionDate ?? DateTime.now())}');
      bytes += generator.hr();

      // Subscriber details in Arabic
      bytes += generator.text('رقم الاشتراك: ${subscriber.subscriberNumber}');
      bytes += generator.text('اسم المشترك: ${subscriber.subscriberName}');
      if (subscriber.meterNumber != null && subscriber.meterNumber!.isNotEmpty) {
        bytes += generator.text('رقم العداد: ${subscriber.meterNumber!}');
      }
      bytes += generator.hr();

      // Payment details in Arabic
      bytes += generator.text('المبلغ المستحق: ${subscriber.currentBalance.toStringAsFixed(2)} ر.ي');
      bytes += generator.text('المبلغ المدفوع: ${amountPaid.toStringAsFixed(2)} ر.ي',
          styles: const PosStyles(
            align: PosAlign.center,
            height: PosTextSize.size2,
            width: PosTextSize.size2,
            bold: true,
          ));
      bytes += generator.text('طريقة الدفع: ${_getPaymentMethodArabic(paymentMethod)}');

      if (collectionResponse.newBalance != null) {
        bytes += generator.text('الرصيد الجديد: ${collectionResponse.newBalance!.toStringAsFixed(2)} ر.ي');
      }

      if (notes != null && notes.isNotEmpty) {
        bytes += generator.text('ملاحظات: $notes');
      }

      // Footer in Arabic
      bytes += generator.text('');
      bytes += generator.text('شكراً لكم',
          styles: const PosStyles(align: PosAlign.center, bold: true));
      bytes += generator.text('');
      bytes += generator.cut();

      return bytes;
    } catch (e) {
      */
      // Fallback to simple Arabic receipt

      //print(e);
      try {
        return 
       /* _generateReceiptLikePDF(
  transactionId: '1754529134645',
  subscriberNumber: '1436179',
  subscriberName: 'منشار البشيري - صلاح حسين محمد',
  meterNumber: '00002243 - البشيري',
 amount: "600"
);*/
        
        
       
       /*   _generateSimpleArabicReceiptNew(
             invoiceReceipt: invoiceReceipt,
         // subscriber: subscriber,
         // collectionResponse: collectionResponse,
         // amountPaid: amountPaid,
         // paymentMethod: paymentMethod,
         // notes: notes,
        );*/

 

              _generateMHTP29LReceipt(
             invoiceReceipt: invoiceReceipt,
         // subscriber: subscriber,
         // collectionResponse: collectionResponse,
         // amountPaid: amountPaid,
         // paymentMethod: paymentMethod,
         // notes: notes,
        ); 


      } catch (e2) {
        // Try MHT-P29L specific method

        print(e2);
        return  [];
      /*  try {
          return _generateMHTP29LReceipt(
            subscriber: subscriber,
            collectionResponse: collectionResponse,
            amountPaid: amountPaid,
            paymentMethod: paymentMethod,
            notes: notes,
          );
        } catch (e3) {
           print(e3);
          // Final fallback to Latin transliteration
          return _generateLatinFallbackReceipt(
            subscriber: subscriber,
            collectionResponse: collectionResponse,
            amountPaid: amountPaid,
            paymentMethod: paymentMethod,
            notes: notes,
          );
        }*/
      }
   // }
  }


  static String _getPaymentMethodArabic(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'نقداً';
      case 'card':
        return 'بطاقة';
      case 'transfer':
        return 'تحويل';
      default:
        return method;
    }
  }

 // MHT-P29L specific receipt generation with GB18030 handling
  static  Future<List<int>>    _generateMHTP29LReceipt  (  {
      required  InvoiceReceipt invoiceReceipt
   // required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
   // required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  })   async {
    List<int> bytes = [];
 

 

 
    // Disable GB18030 and initialize for Arabic
 
      bytes.addAll([27, 64]); // ESC @ - Initialize
    bytes.addAll([28, 46]); // FS . - Cancel GB18030 mode
    bytes.addAll([27, 82, 0]); // ESC R 0 - International character set
    bytes.addAll([27, 116, 3]); // ESC t 3 - UTF-8 codepage
    bytes.addAll([28, 67, 2]); // FS C 2 - Arabic/Hebrew support
 

    // Header
    bytes.addAll([27, 97, 1]); // Center alignment

  bytes.addAll(await _printLogoRaw());

    bytes.addAll(_getMHTP29LArabicBytes('سند تحصيل', large: true, bold: true));
    bytes.addAll(_getMHTP29LArabicBytes('المؤسسة العامة للكهرباء' ,large: true, bold: true));
   // bytes.addAll(_getMHTP29LArabicBytes('================================'));
   // bytes.addAll([10]);

    // Content
 bytes.addAll([27, 97, 2]); // Left alignment
 
    bytes.addAll(_getMHTP29LArabicBytes('رقم السند : ', bold: true) );
     bytes.addAll(_getMHTP29LArabicBytes('${invoiceReceipt.docId }'));
    
    bytes.addAll(_getMHTP29LArabicBytes('تاريخ التحصيل : ' ,  bold: true ));
    bytes.addAll(_getMHTP29LArabicBytes('${_formatDateTime(invoiceReceipt.dateInvoice).replaceAll(' ', '')}'));
 
    //bytes.addAll(_getMHTP29LArabicBytes('الوقت: ${_formatTime(collectionResponse.transactionDate ?? DateTime.now())}'));
   // bytes.addAll([10]);
    //bytes.addAll(_getMHTP29LArabicBytes('================================'));
    //bytes.addAll([10]);

   // bytes.addAll(_getMHTP29LArabicBytes('رقم المشترك : ' ,  bold: true));
    //    bytes.addAll(_getMHTP29LArabicBytes('${invoiceReceipt.subscriberNumber}'));
    bytes.addAll(_getMHTP29LArabicBytes('اسم المشترك : ' ,  bold: true ));
     bytes.addAll(_getMHTP29LArabicBytes('${invoiceReceipt.subscriberName}'));
    /*if (invoiceReceipt.meterNumber != null && invoiceReceipt.meterNumber!.isNotEmpty) {
      bytes.addAll(_getMHTP29LArabicBytes('رقم العداد: ${invoiceReceipt.meterNumber!}'));
    }*/
  //  bytes.addAll([10]);
 //   bytes.addAll(_getMHTP29LArabicBytes('================================'));
    //bytes.addAll([10]);

    bytes.addAll(_getMHTP29LArabicBytes('المبلغ المستحق: ' ,  bold: true));
      bytes.addAll(_getMHTP29LArabicBytes('${invoiceReceipt.dueAmount!.toStringAsFixed(2)} ر.ي'));
    
   
   // bytes.addAll([27, 97, 0]); // Left alignment
   // bytes.addAll(_getMHTP29LArabicBytes('طريقة الدفع: ${_getPaymentMethodArabic(invoiceReceipt.paymentType)}'));

   /* if (invoiceReceipt.newBalance != null) {
      bytes.addAll(_getMHTP29LArabicBytes('الرصيد الجديد: ${invoiceReceipt.newBalance!.toStringAsFixed(2)} ر.ي'));
    }*/
    bytes.addAll(_getMHTP29LArabicBytes('اسم المحصل: ' ,  bold: true ));
        bytes.addAll(_getMHTP29LArabicBytes('${invoiceReceipt.collector}'));
  bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getMHTP29LArabicBytes('المبلغ المدفوع: ${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي',   bold: true , large: true));
    //  bytes.addAll([27, 97, 2]); // Center alignment
     bytes.addAll(_getMHTP29LArabicBytes('طبع بواسطة : ' + '${invoiceReceipt.userPrint!}'));
 
      bytes.addAll(_getMHTP29LArabicBytes('طبع في' + '${_formatDateTime(invoiceReceipt.datePrint)}'  + ' - ' + '${_formatTime(invoiceReceipt.datePrint)}'  ));
  

  /*  if (invoiceReceipt.notes != null && invoiceReceipt.notes!.isNotEmpty) {
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('ملاحظات: $invoiceReceipt.notes'));
    }*/

    // Footer
  //  bytes.addAll([10]);
   // bytes.addAll(_getMHTP29LArabicBytes('================================'));
    bytes.addAll([27, 97, 1]); // Center alignment
   // bytes.addAll(_getMHTP29LArabicBytes('شكراً لكم', bold: true));
   // bytes.addAll([10, 10, 10]);
 
    // Cut paper
    bytes.addAll([29, 86, 65, 3]);

    return bytes;
  }
 
  // Helper function to print Arabic text
  static List<int> _printArabic(Generator generator, String text, {PosStyles? styles}) {
    // Convert Arabic string to bytes using CP1256 encoding
    final arabicBytes = _stringToArabicBytes(text);
    
    // Create a temporary list for the Arabic text
    List<int> result = [];
    
    // Apply styles if provided
    if (styles != null) {
      result += generator.setStyles(styles);
    }
    
    // Add the Arabic bytes directly
    result += arabicBytes;
    
    // Add line feed
    result += generator.feed(1);
    
    return result;
  }

  // Convert Arabic string to CP1256 bytes
  static List<int> _stringToArabicBytes(String text) {
    // Arabic characters in CP1256 encoding
    final arabicMap = {
      'ا': 0xC7, 'ب': 0xC8, 'ت': 0xC9, 'ث': 0xCA, 'ج': 0xCB,
      'ح': 0xCC, 'خ': 0xCD, 'د': 0xCE, 'ذ': 0xCF, 'ر': 0xD0,
      'ز': 0xD1, 'س': 0xD3, 'ش': 0xD4, 'ص': 0xD5, 'ض': 0xD6,
      'ط': 0xD7, 'ظ': 0xD8, 'ع': 0xD9, 'غ': 0xDA, 'ف': 0xDF,
      'ق': 0xE0, 'ك': 0xE3, 'ل': 0xE4, 'م': 0xE5, 'ن': 0xE6,
      'ه': 0xE7, 'و': 0xE8, 'ي': 0xEA, 'ة': 0xC9, 'ء': 0xC1,
      'أ': 0xC3, 'إ': 0xC5, 'آ': 0xC2, 'ى': 0xE9, 'ئ': 0xEB,
      'ؤ': 0xEC, 'ـ': 0x40, 'َ': 0x40, 'ُ': 0x40, 'ِ': 0x40,
      'ّ': 0x40, 'ْ': 0x40, '': 0x20, // Space
    };

    List<int> bytes = [];
    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      if (arabicMap.containsKey(char)) {
        bytes.add(arabicMap[char]!);
      } else {
        // Fallback for non-Arabic characters
        bytes.add(char.codeUnitAt(0));
      }
    }
    return bytes;
  }

  static String _getArabicDate() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  static String _getArabicTime() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }


// MHT-P29L specific Arabic text conversion with UTF-8
  static List<int> _getMHTP29LArabicBytes(String text, {bool bold = false, bool large = false}) {
    List<int> bytes = [];

    // Set formatting
    if (bold) {
      bytes.addAll([27, 69, 1]); // Bold on
       
    }
    if (large) {
      bytes.addAll([29, 33, 1]); // Double size
     
    }else{
 bytes.addAll([29, 33, 0]); // Double size
    }

    // Use UTF-8 encoding directly since we disabled GB18030
    try {
      List<int> utf8Bytes = utf8.encode(text);
      bytes.addAll(utf8Bytes);
    } catch (e) {
      // Fallback to ASCII if UTF-8 fails
      print(text + "Fallback to ASCII if UTF-8 fails") ;

      for (int i = 0; i < text.length; i++) {
        int charCode = text.codeUnitAt(i);
        if (charCode <= 127) {
         // bytes.add(charCode);
        } else {
         // bytes.add(0x3F); // Question mark
        }
      }
    }
    bytes.add(10); // Line feed

    // Reset formatting
    if (bold) {
      bytes.addAll([27, 69, 0]); // Bold off
      
    }
    if (large) {
      bytes.addAll([29, 33, 0]); // Normal size
       
    }

    return bytes;
  }

 

// Helper functions
 

  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour}:${dateTime.minute}';
  }


   // Final fallback with Latin transliteration
  static List<int> _generateLatinFallbackReceipt({
      required  InvoiceReceipt invoiceReceipt
  //  required Subscriber subscriber,
  //  required CollectionResponse collectionResponse,
  //  required double amountPaid,
  //  required String paymentMethod,
  //  String? notes,
  }) {
    List<int> bytes = [];

    // Initialize printer with basic settings
    bytes.addAll([27, 64]); // ESC @ - Initialize
    bytes.addAll([27, 97, 1]); // Center alignment

    // Header in Arabic transliteration
    bytes.addAll(_getBasicTextBytes('SAND TAHSEEL', large: true, bold: true));
    bytes.addAll(_getBasicTextBytes('AL-MUASSASA AL-AMMA LIL-KAHRABA'));
    bytes.addAll(_getBasicTextBytes('================================'));
    bytes.addAll([10]);

    // Content
    bytes.addAll([27, 97, 0]); // Left alignment
    bytes.addAll(_getBasicTextBytes('Raqam Al-Muamala: ${invoiceReceipt.docId ?? 'Ghair Muhadad'}'));
    bytes.addAll(_getBasicTextBytes('Al-Tareekh: ${_formatDateTime(invoiceReceipt.dateInvoice  )}'));
        bytes.addAll(_getBasicTextBytes('Al-Tareekh: ${_formatDateTime(invoiceReceipt.datePrint  )}'));
   // bytes.addAll(_getBasicTextBytes('Al-Waqt: ${_formatTime(collectionResponse.transactionDate ?? DateTime.now())}'));
    bytes.addAll([10]);
    bytes.addAll(_getBasicTextBytes('================================'));
    bytes.addAll([10]);

    bytes.addAll(_getBasicTextBytes('Raqam Al-Ishtirak: ${invoiceReceipt.subscriberNumber}'));
    bytes.addAll(_getBasicTextBytes('Ism Al-Mushtarik: ${_transliterateArabicName(invoiceReceipt.subscriberName!)}'));
    if (invoiceReceipt.meterNumber != null && invoiceReceipt.meterNumber!.isNotEmpty) {
      bytes.addAll(_getBasicTextBytes('Raqam Al-Addad: ${invoiceReceipt.meterNumber!}'));
    }
    bytes.addAll([10]);
    bytes.addAll(_getBasicTextBytes('================================'));
    bytes.addAll([10]);

    bytes.addAll(_getBasicTextBytes('Al-Mablagh Al-Mustahaq: ${invoiceReceipt.dueAmount!.toStringAsFixed(2)} YER'));
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getBasicTextBytes('AL-MABLAGH AL-MADFU: ${invoiceReceipt.paidAmount.toStringAsFixed(2)} YER', large: true, bold: true));
    bytes.addAll([27, 97, 0]); // Left alignment
    bytes.addAll(_getBasicTextBytes('Tariiqat Al-Daf: ${_getPaymentMethodTransliterated(invoiceReceipt.paymentType)}'));

    /*if (invoiceReceipt.newBalance != null) {
      bytes.addAll(_getBasicTextBytes('Al-Raseed Al-Jadeed: ${invoiceReceipt.newBalance!.toStringAsFixed(2)} YER'));
    }*/
    bytes.addAll(_getBasicTextBytes('Ism Al-Muhasel: ${_transliterateArabicName(invoiceReceipt.collector!)}'));
    if (invoiceReceipt.notes != null && invoiceReceipt.notes!.isNotEmpty) {
      bytes.addAll([10]);
      bytes.addAll(_getBasicTextBytes('Mulahazat: ${_transliterateArabicName(invoiceReceipt.notes!)}'));
    }

    // Footer
    bytes.addAll([10]);
    bytes.addAll(_getBasicTextBytes('================================'));
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getBasicTextBytes('SHUKRAN LAKUM', bold: true));
    bytes.addAll([10, 10, 10]);

    // Cut paper
    bytes.addAll([29, 86, 65, 3]);

    return bytes;
  }

  static String _getPaymentMethodTransliterated(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'NAQDAN';
      case 'card':
        return 'BITAQA';
      case 'transfer':
        return 'TAHWEEL';
      default:
        return method.toUpperCase();
    }
  }


// Basic text conversion for fallback
  static List<int> _getBasicTextBytes(String text, {bool bold = false, bool large = false}) {
    List<int> bytes = [];

    if (bold) {
      bytes.addAll([27, 69, 1]); // Bold on
    }
    if (large) {
      bytes.addAll([29, 33, 17]); // Double size
    }

    // Use ASCII encoding
    for (int i = 0; i < text.length; i++) {
      int charCode = text.codeUnitAt(i);
      if (charCode <= 127) {
        bytes.add(charCode);
      } else {
        bytes.add(0x3F); // Question mark for non-ASCII
      }
    }
    bytes.add(10); // Line feed

    if (bold) {
      bytes.addAll([27, 69, 0]); // Bold off
    }
    if (large) {
      bytes.addAll([29, 33, 0]); // Normal size
    }

    return bytes;
  }

   // Convert Arabic text to MHT-P29L specific encoding
  static List<int> _convertToWindows1256(String text) {
    // MHT-P29L Arabic character mapping (CP864 based)
    Map<String, int> arabicMap = {
      // Arabic letters - MHT-P29L specific codes
      'ا': 0x8D, 'أ': 0x8E, 'إ': 0x8F, 'آ': 0x90, 'ب': 0x91, 'ت': 0x92, 'ث': 0x93,
      'ج': 0x94, 'ح': 0x95, 'خ': 0x96, 'د': 0x97, 'ذ': 0x98, 'ر': 0x99, 'ز': 0x9A,
      'س': 0x9B, 'ش': 0x9C, 'ص': 0x9D, 'ض': 0x9E, 'ط': 0x9F, 'ظ': 0xA0, 'ع': 0xA1,
      'غ': 0xA2, 'ف': 0xA3, 'ق': 0xA4, 'ك': 0xA5, 'ل': 0xA6, 'م': 0xA7, 'ن': 0xA8,
      'ه': 0xA9, 'و': 0xAA, 'ي': 0xAB, 'ى': 0xAC, 'ة': 0xAD, 'ء': 0xAE,
      // Arabic numbers
      '٠': 0xB0, '١': 0xB1, '٢': 0xB2, '٣': 0xB3, '٤': 0xB4, '٥': 0xB5,
      '٦': 0xB6, '٧': 0xB7, '٨': 0xB8, '٩': 0xB9,
      // Common symbols
      ' ': 0x20, '.': 0x2E, ',': 0x2C, ':': 0x3A, '-': 0x2D, '_': 0x5F,
      '(': 0x28, ')': 0x29, '/': 0x2F, '=': 0x3D,
    };

    List<int> result = [];
    for (int i = 0; i < text.length; i++) {
      String char = text[i];
      if (arabicMap.containsKey(char)) {
        result.add(arabicMap[char]!);
      } else {
        // For English characters and numbers, use ASCII
        try {
          result.add(char.codeUnitAt(0));
        } catch (e) {
          result.add(0x3F); // Question mark for unknown characters
        }
      }
    }
    return result;
  }
 // Transliterate Arabic names to readable Latin
  static String _transliterateArabicName(String arabicText) {
    Map<String, String> transliterationMap = {
      'أ': 'A', 'إ': 'I', 'آ': 'AA', 'ا': 'A', 'ب': 'B', 'ت': 'T', 'ث': 'TH',
      'ج': 'J', 'ح': 'H', 'خ': 'KH', 'د': 'D', 'ذ': 'DH', 'ر': 'R', 'ز': 'Z',
      'س': 'S', 'ش': 'SH', 'ص': 'S', 'ض': 'D', 'ط': 'T', 'ظ': 'Z', 'ع': 'A',
      'غ': 'GH', 'ف': 'F', 'ق': 'Q', 'ك': 'K', 'ل': 'L', 'م': 'M', 'ن': 'N',
      'ه': 'H', 'و': 'W', 'ي': 'Y', 'ى': 'A', 'ة': 'H', 'ء': 'A',
      ' ': ' ', '.': '.', ',': ',', '-': '-', '_': '_',
    };

    String result = '';
    for (int i = 0; i < arabicText.length; i++) {
      String char = arabicText[i];
      result += transliterationMap[char] ?? char;
    }
    return result;
  }

 // Fallback method with MHT-P29L specific Arabic encoding
  static List<int> _generateSimpleArabicReceipt({
      required  InvoiceReceipt invoiceReceipt
  //  required Subscriber subscriber,
  //  required CollectionResponse collectionResponse,
  //  required double amountPaid,
 //   required String paymentMethod,
 //   String? notes,
  }) {
    List<int> bytes = [];



   



    // Initialize printer with MHT-P29L specific settings
  bytes.addAll([27, 64]); // ESC @ - Initialize
    bytes.addAll([28, 46]); // FS . - Cancel GB18030 mode
    bytes.addAll([27, 82, 0]); // ESC R 0 - International character set
    bytes.addAll([27, 116, 3]); // ESC t 3 - UTF-8 codepage
    bytes.addAll([28, 67, 2]); // FS C 2 - Arabic/Hebrew support


    // Header
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getMHTP29LArabicBytes('سند تحصيل', large: true, bold: true));
    bytes.addAll(_getMHTP29LArabicBytes('المؤسسة العامة للكهرباء'));
    bytes.addAll(_getMHTP29LArabicBytes('================================'));
    bytes.addAll([10]);

    // Content
   // bytes.addAll([27, 97, 0]); // Left alignment
        bytes.addAll([27, 97, 2]); // Center alignment
    bytes.addAll(_getMHTP29LArabicBytes('رقم السند: ${invoiceReceipt.docId ?? 'غير محدد'}'));
    bytes.addAll(_getMHTP29LArabicBytes('تاريخ التحصيل: ${_formatDateTime(invoiceReceipt.dateInvoice  )}'));
    bytes.addAll(_getMHTP29LArabicBytes('تاريخ الطباعة: ${_formatDateTime(invoiceReceipt.datePrint  )}'));
   // bytes.addAll(_getMHTP29LArabicBytes('الوقت: ${_formatTime(collectionResponse.transactionDate ?? DateTime.now())}'));
    bytes.addAll([10]);
    bytes.addAll(_getMHTP29LArabicBytes('================================'));
    bytes.addAll([10]);

    bytes.addAll(_getMHTP29LArabicBytes('رقم الاشتراك: ${invoiceReceipt.subscriberNumber}'));
    bytes.addAll(_getMHTP29LArabicBytes('اسم المشترك: ${invoiceReceipt.subscriberName}'));
    if (invoiceReceipt.meterNumber != null && invoiceReceipt.meterNumber!.isNotEmpty) {
      bytes.addAll(_getMHTP29LArabicBytes('رقم العداد: ${invoiceReceipt.meterNumber!}'));
    }
    bytes.addAll([10]);
    bytes.addAll(_getMHTP29LArabicBytes('================================'));
    bytes.addAll([10]);

    bytes.addAll(_getMHTP29LArabicBytes('المبلغ المستحق: ${invoiceReceipt.dueAmount!.toStringAsFixed(2)} ر.ي'));
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getMHTP29LArabicBytes('المبلغ المدفوع: ${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي', large: true, bold: true));
    bytes.addAll([27, 97, 2]); // Left alignment
    bytes.addAll(_getMHTP29LArabicBytes('طريقة الدفع: ${_getPaymentMethodArabic(invoiceReceipt.paymentType)}'));

  /*  if (invoiceReceipt.newBalance != null) {
      bytes.addAll(_getMHTP29LArabicBytes('الرصيد الجديد: ${invoiceReceipt.newBalance!.toStringAsFixed(2)} ر.ي'));
    }*/
  bytes.addAll(_getMHTP29LArabicBytes('اسم المحصل: ${invoiceReceipt.collector}'));
    if (invoiceReceipt.notes != null && invoiceReceipt.notes!.isNotEmpty) {
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('ملاحظات: $invoiceReceipt.notes'));
    }

    // Footer
    bytes.addAll([10]);
    bytes.addAll(_getMHTP29LArabicBytes('================================'));
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getMHTP29LArabicBytes('شكراً لكم', bold: true));
    bytes.addAll([10, 10, 10]);

    // Cut paper
    bytes.addAll([29, 86, 65, 3]);

    return bytes;
  }


static List<int> _generateReceiptLikePDF({
  required String transactionId,
  required String subscriberNumber,
  required String subscriberName,
  required String meterNumber,
  required String amount,
}) {
  List<int> bytes = [];
  
  // 1. الطابعة تهيئة
  bytes.addAll([27, 64, 27, 116, 5, 28, 67, 2]);
  
  // 2. دالة المحاذاة الذكية
  void _printAligned(String label, String value, {bool bold = false}) {
    const lineWidth = 48;
    int labelLen = utf8.encode(label).length;
    int valueLen = utf8.encode(value).length;
    int spaces = lineWidth - labelLen - valueLen;
    
    if (bold) bytes.addAll([27, 69, 1]);
    bytes.addAll(utf8.encode(label + (spaces > 0 ? ' ' * spaces : ' ') + value));
    bytes.addAll([10]);
    if (bold) bytes.addAll([27, 69, 0]);
  }

  // 3. طباعة الفاتورة
  bytes.addAll([27, 97, 1, 29, 33, 17]); // مركز + كبير
  bytes.addAll(utf8.encode('سند تحصيل'));
  bytes.addAll([10, 29, 33, 1]);
  
  bytes.addAll([27, 97, 2]); // محاذاة لليسار
  
  _printAligned('رقم المعاملة :', transactionId);
  _printAligned('رقم المشترك :', subscriberNumber);
  _printAligned('اسم المشترك :', subscriberName);
  _printAligned('رقم العداد :', meterNumber);
  _printAligned('المبلغ :', '$amount ر.ي', bold: true);
  
  bytes.addAll([10, 10, 29, 86, 65, 3]); // قص الورق
  
  return bytes;
}
 
// Helper function to format date as yyyy-MM-dd
/*static String _formatDate(DateTime date) {
  return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
}*/

static Future<List<int>?> _prepareLogoFor88mm(String imagePath) async {
  const targetWidth = 400; // أفضل عرض لطابعة 88مم
  const maxHeight = 400; // أقصى ارتفاع آمن
  
  try {

      final logoData = await rootBundle.load('assets/images/peclogo.png');

  //  final imageFile = File(imagePath);
  //  if (!await imageFile.exists()) return null;

    final originalImage = img.decodeImage(logoData.buffer.asUint8List());
    if (originalImage == null) return null;

    // حساب الارتفاع مع الحفاظ على نسبة العرض إلى الارتفاع
    final ratio = originalImage.height / originalImage.width;
    final targetHeight = (targetWidth * ratio).round().clamp(0, maxHeight);

    // تغيير المقاس مع تحسين الجودة
    final resizedImage = img.copyResize(
      originalImage,
      width: targetWidth,
      height: targetHeight,
      interpolation: img.Interpolation.cubic,
    );

    // تحويل إلى أبيض وأسود بحد أدنى 150
    final threshold = 150; // زيادة التباين
    final blackWhite = img.grayscale(resizedImage);
    
    return _encodeEscPosImage(blackWhite, threshold);
  } catch (e) {
    print('خطأ في معالجة اللوجو: $e');
    return null;
  }
}

static Future<List<int>?> _prepareLogo(String imagePath) async {
  try {
   // final imageFile = File(imagePath);
  //  if (!await imageFile.exists()) return null;

  final logoData = await rootBundle.load('assets/images/peclogo.png');
    // تحميل الصورة
    final originalImage = img.decodeImage(await logoData.buffer.asUint8List());
    if (originalImage == null) return null;

 

 
    // تغيير المقاس (عرض 72 بكسل مع الحفاظ على النسبة)
     final resizedImage = img.copyResize(originalImage, width: 72 );

  

    // تحويل إلى أبيض وأسود
    final grayscale = img.grayscale(resizedImage);
    final threshold = 128; // حد التحويل للأبيض/الأسود
  final blackWhite = img.copyResize(grayscale, width: 72  );


 

    // تحويل الصورة إلى بيانات الطابعة
    return _encodeEscPosImage(blackWhite, threshold);
  } catch (e) {
    print('Error processing logo: $e');
    return null;
  }
}

 
 
static List<int> _encodeEscPosImage(img.Image image, int threshold) {
  List<int> bytes = [];
  bytes.addAll([0x1D, 0x76, 0x30, 0x00]); // GS v 0
  
  final width = image.width;
  final height = image.height;
  
  bytes.addAll([(width ~/ 8), width % 8]);
  bytes.addAll([height ~/ 256, height % 256]);
  
  for (int y = 0; y < height; y++) {
    for (int x = 0; x < width ~/ 8; x++) {
      int byte = 0;
      for (int b = 0; b < 8; b++) {
        final pixel = image.getPixel(x * 8 + b, y);
        if (img.getLuminance(pixel) < threshold) {
          byte |= 1 << (7 - b);
        }
      }
      bytes.add(byte);
    }
  }
  
  return bytes;
}

 static Future<List<int>> _generateSimpleArabicReceiptNew(   {  
   required  InvoiceReceipt invoiceReceipt
 // required Subscriber subscriber,
 // required CollectionResponse collectionResponse,
 // required double amountPaid,
 // required String paymentMethod,
 // String? notes,
})  async {


     
     
  List<int> bytes = [];
 
  //final logoBytes = await _prepareLogo( 'assets/images/peclogo.png');


//  if (logoBytes != null) {
//    bytes.addAll(logoBytes);
 //   bytes.addAll([10, 10]); // مسافتان بعد اللوجو
        
      
//  }


// 3. إرسال الصورة
 
  // 1. تحميل وتحويل اللوجو بالمقاس الأمثل
 




  // Initialize printer with settings for 88mm width
  bytes.addAll([27, 64]); // Initialize printer
  bytes.addAll([28, 46]); // Cancel GB18030 mode
  bytes.addAll([27, 82, 0]); // International character set
  bytes.addAll([27, 116, 5]); // RTL for Arabic
  bytes.addAll([28, 67, 2]); // Arabic/Hebrew support
  bytes.addAll([29, 33, 0]); // Reset text size

  // Header - Center aligned
  bytes.addAll([27, 97, 1]); // Center alignment
     // 1. تحميل وتحويل اللوجو


  bytes.addAll(_getMHTP29LArabicBytes('سند تحصيل', large: true, bold: true));
  bytes.addAll(_getMHTP29LArabicBytes('المؤسسة العامة للكهرباء', bold: true));
  //bytes.addAll(_getMHTP29LArabicBytes('================================'));
 //utf8.encode('سند تحصيل')

  // Helper function for right-left alignment (88mm width = ~48 characters)
  void _printAligned(String label, String value, {bool bold = false}) {
    const lineWidth = 48;
    int labelLen = utf8.encode(label).length;
    int valueLen = utf8.encode(value).length;
    int spaces = lineWidth - labelLen - valueLen;
    
    if (bold) bytes.addAll([27, 69, 1]);
    bytes.addAll(utf8.encode(label + (spaces > 0 ? ' ' * spaces : ' ') + value));
       // bytes.addAll( _getMHTP29LArabicBytes(label + (spaces > 0 ? ' ' * spaces : ' ') + value) );
    bytes.addAll([3]);
    if (bold) bytes.addAll([27, 69, 0]);
  }
   // bytes.addAll([29, 33, 0]); // Reset text size
  
  bytes.addAll([27, 97, 2]); // محاذاة لليسار
    ///bytes.addAll(_getMHTP29LArabicBytes('رقم السند       : ' +'${_formatDateTime(invoiceReceipt.dateInvoice)}'   , bold: false));
  //  _printAligned('رقم السند       : ', invoiceReceipt.docId.toString() ?? 'غير محدد');
  // Transaction details

  _printAligned('رقم السند       : ', invoiceReceipt.docId.toString() ?? 'غير محدد');
  _printAligned('تاريخ التحصيل          : ', _formatDateTime(invoiceReceipt.dateInvoice));
  _printAligned('تاريخ الطباعة          : ', _formatDateTime(invoiceReceipt.datePrint ));
  //_printAligned('     الوقت', _formatTime(collectionResponse.transactionDate ?? DateTime.now()));
  /*
  bytes.addAll([5]);
//  bytes.addAll(_getMHTP29LArabicBytes('================================================'));
//  bytes.addAll([5]);

  // Subscriber details
  _printAligned('رقم المشترك    : ', invoiceReceipt.subscriberNumber!);
  _printAligned('اسم المشترك     : ', invoiceReceipt.subscriberName!);
   // _printAligned('رقم العداد     : ', invoiceReceipt.meterNumber!);
 /* if (subscriber.meterNumber != null && subscriber.meterNumber!.isNotEmpty) {
  _printAligned('   رقم العداد', subscriber.meterNumber!);
  }*/
  
  bytes.addAll([5]);
 // bytes.addAll(_getMHTP29LArabicBytes('================================================'));
  //bytes.addAll([5]);

  // Payment details
  _printAligned('المبلغ المستحق  : ', '${invoiceReceipt.dueAmount!.toStringAsFixed(2)} ر.ي');
  _printAligned('طريقة الدفع     : ', _getPaymentMethodArabic(invoiceReceipt.paymentType));
  /*if (invoiceReceipt.newBalance != null) {
  _printAligned('   الرصيد الجديد', '${invoiceReceipt.newBalance!.toStringAsFixed(2)} ر.ي');
  }*/
  _printAligned('اسم المحصل     : ', invoiceReceipt.collector!);
  // Amount paid - Center aligned and bold
  bytes.addAll([27, 97, 1]); // Center alignment
  bytes.addAll(_getMHTP29LArabicBytes('المبلغ المدفوع: ${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي', bold: true));
  bytes.addAll([27, 97, 2]); // Left alignment
  
  // Notes if available
 /* if (invoiceReceipt.notes != null && invoiceReceipt.notes!.isNotEmpty) {
    bytes.addAll([5]);
    bytes.addAll(_getMHTP29LArabicBytes('ملاحظات: $invoiceReceipt.notes'));
  }*/
*/
  // Footer
  bytes.addAll([10]);
 // bytes.addAll(_getMHTP29LArabicBytes('================================================'));
  bytes.addAll([27, 97, 1]); // Center alignment
  bytes.addAll(_getMHTP29LArabicBytes('شكراً لكم', bold: true));
  bytes.addAll([10, 10, 10]);

  // Cut paper (partial cut for 88mm printers)
  bytes.addAll([29, 86, 65, 3]);

  return bytes; 
}





// Convert Arabic text to bytes with proper Windows-1256 encoding
  static List<int> _getArabicTextBytes(String text, {bool bold = false, bool large = false}) {
    List<int> bytes = [];

    // Set text formatting
    if (bold) {
      bytes.addAll([27, 69, 1]); // ESC E 1 - Bold on
    }
    if (large) {
      bytes.addAll([29, 33, 17]); // GS ! 17 - Double width and height
    }

    // Convert Arabic text to Windows-1256 encoding
    List<int> textBytes = _convertToWindows1256(text);
    bytes.addAll(textBytes);
    bytes.add(10); // Line feed

    // Reset formatting
    if (bold) {
      bytes.addAll([27, 69, 0]); // ESC E 0 - Bold off
    }
    if (large) {
      bytes.addAll([29, 33, 0]); // GS ! 0 - Normal size
    }

    return bytes;
  }




  // Generate simple receipt with basic text (Android/iOS)
 /* static Future<List<int>> _generateSimpleReceipt({
    required Subscriber subscriber,
    required CollectionResponse collectionResponse,
    required double amountPaid,
    required String paymentMethod,
    String? notes,
  }) async {
    List<int> bytes = [];

    // Initialize printer
    bytes.addAll([27, 64]); // ESC @ - Initialize

    // Header - Center aligned
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getSimpleTextBytes('COLLECTION RECEIPT', bold: true, large: true));
    bytes.addAll(_getSimpleTextBytes('ELECTRICITY AUTHORITY'));
    bytes.addAll(_getSimpleTextBytes('================================'));
    bytes.addAll([10]);

    // Content - Left aligned
    bytes.addAll([27, 97, 0]); // Left alignment
    bytes.addAll(_getSimpleTextBytes('Transaction: ${collectionResponse.transactionId ?? 'N/A'}'));
    bytes.addAll(_getSimpleTextBytes('Date: ${_formatDateTime(collectionResponse.transactionDate ?? DateTime.now())}'));
    bytes.addAll(_getSimpleTextBytes('Time: ${_formatTime(collectionResponse.transactionDate ?? DateTime.now())}'));
    bytes.addAll([10]);
    bytes.addAll(_getSimpleTextBytes('================================'));
    bytes.addAll([10]);

    bytes.addAll(_getSimpleTextBytes('Subscriber: ${subscriber.subscriberNumber}'));
    bytes.addAll(_getSimpleTextBytes('Name: ${subscriber.subscriberName}'));
    if (subscriber.meterNumber != null && subscriber.meterNumber!.isNotEmpty) {
      bytes.addAll(_getSimpleTextBytes('Meter: ${subscriber.meterNumber!}'));
    }
    bytes.addAll([10]);
    bytes.addAll(_getSimpleTextBytes('================================'));
    bytes.addAll([10]);

    bytes.addAll(_getSimpleTextBytes('Amount Due: ${subscriber.currentBalance.toStringAsFixed(2)} YER'));
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getSimpleTextBytes('PAID: ${amountPaid.toStringAsFixed(2)} YER', large: true, bold: true));
    bytes.addAll([27, 97, 0]); // Left alignment
    bytes.addAll(_getSimpleTextBytes('Method: ${_getPaymentMethodEnglish(paymentMethod)}'));

    if (collectionResponse.newBalance != null) {
      bytes.addAll(_getSimpleTextBytes('New Balance: ${collectionResponse.newBalance!.toStringAsFixed(2)} YER'));
    }

    if (notes != null && notes.isNotEmpty) {
      bytes.addAll([10]);
      bytes.addAll(_getSimpleTextBytes('Notes: $notes'));
    }

    // Footer
    bytes.addAll([10]);
    bytes.addAll(_getSimpleTextBytes('================================'));
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getSimpleTextBytes('THANK YOU', bold: true));
    bytes.addAll([10, 10, 10]);

    // Cut paper
    bytes.addAll([29, 86, 65, 3]);

    return bytes;
  }
*/
  // Print to Windows system printer
  static Future<void> _printToWindowsSystemPrinter({
   required  InvoiceReceipt invoiceReceipt
   // required Subscriber subscriber,
   // required CollectionResponse collectionResponse,
  //  required double amountPaid,
   // required String paymentMethod,
   // String? notes,
  }) async {
    try {
      if (kDebugMode) {
        print('Printing to Windows system printer...');
      }

      // Check if Windows system printer is connected
      if (!WindowsSystemPrinter.isConnected()) {
        throw Exception('طابعة النظام غير متصلة. يرجى اختيار طابعة من الإعدادات.');
      }

      // Create CollectionReport from the data
    /*  final collectionReport = 
        CollectionReport(
        transactionId: invoiceReceipt.transactionId?.toString() ?? '',
        subscriberNumber: subscriber.subscriberNumber,
        subscriberName: subscriber.subscriberName,
        address: subscriber.address,
        meterNumber: subscriber.meterNumber,
        amountDue: subscriber.currentBalance,
        amountPaid: amountPaid,
        paymentMethod: paymentMethod,
        newBalance: collectionResponse.newBalance ?? 0.0,
        transactionDate: collectionResponse.transactionDate ?? DateTime.now() ,
        notes: notes,
        collectorName: 'المحصل', // Will be updated with actual user name
      ); */

      // Get current user (you might need to pass this as parameter)
      /*final currentUser = User(
        id: 1,
        username: 'collector',
        fullName: 'المحصل',
        permissions: [],
      );*/

      // Print using Windows system printer
      final success = await WindowsSystemPrinter.printCollectionReceipt(
           invoiceReceipt 
        //collectionReport,
        //currentUser,
      );

      if (!success) {
        throw Exception('فشل في الطباعة على طابعة النظام');
      }

      if (kDebugMode) {
        print('Windows system printer: Print completed successfully');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Windows system printer error: $e');
      }
      throw Exception('خطأ في طباعة النظام: ${e.toString()}');
    }
  }



static Future<Uint8List> convertPdfToImage(pw.Document pdf) async {
  // 1. تحويل PDF إلى صفحة صور
  final Uint8List pdfBytes = await pdf.save();
  final List<Uint8List> pageImages = (  Printing.raster(
    pdfBytes,
    dpi: 200, // دقة الصورة
  )) as List<Uint8List>;

  // 2. إذا كان لديك صفحات متعددة، يمكنك دمجها هنا
  // في هذا المثال سنأخذ الصفحة الأولى فقط
  final Uint8List firstPageImage = pageImages.first;

  // 3. تحويل إلى تنسيق Image (اختياري إذا كنت تريد معالجة الصورة)
  final img.Image? image = img.decodeImage(firstPageImage);
  
  // 4. يمكنك هنا إجراء أي تعديلات على الصورة قبل إرجاعها
  // مثلاً: تغيير الحجم، إضافة علامة مائية، إلخ.
  
  return firstPageImage;
}



  // Test print with Arabic text
  static Future<bool> testPrint() async {
    try {

    //  _loadArabicFonts();
      if (kDebugMode) {
        print('Starting test print...');
        print('Platform: ${Platform.operatingSystem}');
      }

      // Windows-specific test print
      if (Platform.isWindows) {
        if (kDebugMode) {
          print('Using Windows-specific test print...');
        }
        return await WindowsSystemPrinter.testPrint();
      }

      // Android/iOS test print
      if (!isConnected()) {
        if (kDebugMode) {
          print('Printer not connected for test print');
        }
        return false;
      }



/*

  final pdf = pw.Document();

      // Use thermal printer paper size (80mm width)
      const thermalFormat = PdfPageFormat(
        80 * PdfPageFormat.mm, // 80mm width
        double.infinity, // Auto height
        marginAll: 2 * PdfPageFormat.mm, // Small margins like thermal printer
      );

      pdf.addPage(
        pw.Page(
          pageFormat: thermalFormat,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header - Center aligned like thermal printer
                pw.Center(
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'اختبار الطباعة',
                                  style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,

                  
                      
                      ),
                      pw.SizedBox(height: 2),
                      pw.Text(
                        'تطبيق EleCollect',
                                        style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                      ),
                      pw.Text(
                        '================================',
                        style: pw.TextStyle(fontSize: 8),
                      ),
                    ],
                  ),
                ),
                pw.SizedBox(height: 4),

                // Test content - Left aligned like thermal printer
                pw.Text(
                  'اختبار طباعة النظام',
                                 style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),
           
                pw.Text(
                  'التاريخ: ${_formatDateTime(DateTime.now())}',
                                  style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الوقت: ${_formatTime(DateTime.now())}',
                                style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 2),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.SizedBox(height: 2),

                // Test Arabic text
                pw.Text(
                  'دعم اللغة العربية: نعم',
                                 style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الأرقام: ١٢٣٤٥٦٧٨٩٠',
                                 style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),

                // Footer
                pw.SizedBox(height: 4),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.Center(
                  child: pw.Text(
                    'تم الاختبار بنجاح',
                                   style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.SizedBox(height: 6), // Extra space like thermal printer
              ],
            );
          },
        ),
      );
      // Print the document

       final Uint8List imageBytes = await convertPdfToImage(pdf);

*/




      // Generate simple test receipt
      final List<int> bytes = await  _generateSimpleTestReceipt();

      // Send print data with timeout for Windows
      if (Platform.isWindows) {
        if (kDebugMode) {
          print('Sending print data to Windows printer...');
        }
        final bool result = await PrintBluetoothThermal.writeBytes(bytes).timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            if (kDebugMode) {
              print('Print timeout on Windows');
            }
            return false;
          },
        );
        if (kDebugMode) {
          print('Windows print result: $result');
        }
        return result;
      } else {
        final bool result = await PrintBluetoothThermal.writeBytes(bytes);
        return result;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Test print error: $e');
      }
      return false;
    }
  }

  static pw.Font? _arabicFont;
  static pw.Font? _arabicBoldFont;
  static pw.Font? _fallbackFont;
  static pw.Font? _fallbackBoldFont;
  static pw.ImageProvider? _logoImage;
  
    static Future<void> _loadArabicFonts() async {
    if (_arabicFont == null || _arabicBoldFont == null) {
      try {
        // Load Google Fonts with Arabic support and fallback fonts
        final arabicRegular = await PdfGoogleFonts.notoSansArabicRegular();
        final arabicBold = await PdfGoogleFonts.notoSansArabicBold();
        final fallbackRegular = await PdfGoogleFonts.notoSansRegular();
        final fallbackBold = await PdfGoogleFonts.notoSansBold();

        _arabicFont = arabicRegular;
        _arabicBoldFont = arabicBold;
        _fallbackFont = fallbackRegular;
        _fallbackBoldFont = fallbackBold;
      } catch (e) {
        // Fallback to basic fonts
        _arabicFont = await PdfGoogleFonts.notoSansRegular();
        _arabicBoldFont = await PdfGoogleFonts.notoSansBold();
      }
    }

    // Load logo if not already loaded
     // Load logo if not already loaded
    if (_logoImage == null) {
      try {
        final logoData = await rootBundle.load('assets/images/peclogo.png');
        _logoImage = pw.MemoryImage(logoData.buffer.asUint8List());
      } catch (e) {
        // Logo loading failed, continue without logo
        _logoImage = null;
      }
    }
  }

  static pw.TextStyle _createTextStyleWithFallback({
    required double fontSize,
    required bool isBold,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
      font: isBold ? _arabicBoldFont : _arabicFont,
      fontFallback: [
        // Arabic fonts
        if (_arabicBoldFont != null && _arabicFont != null) ...[
          isBold ? _arabicBoldFont! : _arabicFont!,
        ],
        // Fallback fonts
        if (_fallbackBoldFont != null && _fallbackFont != null) ...[
          isBold ? _fallbackBoldFont! : _fallbackFont!,
        ],
      ],
    );
  }


  // Helper methods
  static String _formatDateTime(DateTime dateTime) {
     return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  /*static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }*/









  static String _getPaymentMethodEnglish(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'Cash';
      case 'card':
        return 'Card';
      case 'transfer':
        return 'Transfer';
      default:
        return method;
    }
  }

  // Simple text conversion for basic thermal printing
  static List<int> _getSimpleTextBytes(String text, {bool bold = false, bool large = false}) {
    List<int> bytes = [];

    // Set formatting
    if (bold) {
      bytes.addAll([27, 69, 1]); // Bold on
    }
    if (large) {
      bytes.addAll([29, 33, 17]); // Double size
    }

    // Use ASCII encoding for basic text
    for (int i = 0; i < text.length; i++) {
      int charCode = text.codeUnitAt(i);
      if (charCode <= 127) {
        bytes.add(charCode);
      } else {
        bytes.add(0x3F); // Question mark for non-ASCII
      }
    }
    bytes.add(10); // Line feed

    // Reset formatting
    if (bold) {
      bytes.addAll([27, 69, 0]); // Bold off
    }
    if (large) {
      bytes.addAll([29, 33, 0]); // Normal size
    }

    return bytes;
  }





  // Get selected printer
  static BluetoothInfo? getSelectedPrinter() {
    return _selectedPrinter;
  }



 
 static Future<List<int>> _printLogoRaw() async {
  List<int> bytes = [];
  
  try {
    final ByteData byteData = await rootBundle.load('assets/images/peclogo.png');
    final Uint8List imageBytes = byteData.buffer.asUint8List();
    final image = img.decodeImage(imageBytes);
    
    if (image != null) {
      // RESIZE TO SMALLER DIMENSIONS
      final resizedImage = img.copyResize(image, width: 200); // Reduced from 384 to 200
      
      // Convert to 1-bit monochrome
      final monochromeImage = _convertToMonochrome(resizedImage);
      
      // Print using raster bitmap format
      bytes.addAll(_printRasterImage(monochromeImage));
    }
  } catch (e) {
    bytes.addAll(_printTextLogo());
  }
  
  return bytes;
}
 static img.Image _convertToMonochrome(img.Image image) {
    final mono = img.Image(width: image.width, height: image.height);
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = (0.299 * pixel.r + 0.587 * pixel.g + 0.114 * pixel.b);
        final value = luminance < 128 ? 0 : 255; // Threshold
        mono.setPixel(x, y, img.ColorRgb8(value, value, value));
      }
    }
    
    return mono;
  }
  static List<int> _printTextLogo() {
    List<int> bytes = [];
    
    bytes.addAll([27, 97, 1]); // ESC a 1 - Center align
    bytes.addAll([29, 33, 17]); // GS ! 17 - Double size
    bytes.addAll(_textToBytes('══════════════'));
    bytes.addAll(_textToBytes(' MY STORE '));
    bytes.addAll(_textToBytes('══════════════'));
    bytes.addAll([29, 33, 0]); // GS ! 0 - Normal size
    bytes.addAll(_textToBytes('Since 2024'));
    bytes.addAll([27, 97, 0]); // ESC a 0 - Left align
    
    return bytes;
  }

  static List<int> _textToBytes(String text) {
    return [...text.codeUnits, 10]; // Text + line feed (LF)
  }

  static List<int> _drawLine() {
    List<int> bytes = [];
    bytes.addAll([27, 97, 0]); // ESC a 0 - Left align
    bytes.addAll(_textToBytes('-' * 42)); // 42 dashes for 80mm paper
    return bytes;
  }
  
  static List<int> _printRasterImage(img.Image image) {
    List<int> bytes = [];
    
    // Set line spacing to 0 for image printing
    bytes.addAll([27, 51, 0]); // ESC 3 0
    
    // Calculate width in bytes (8 pixels per byte)
    final widthBytes = (image.width + 7) ~/ 8;
    
    // Print image line by line
    for (int y = 0; y < image.height; y++) {
      // GS v 0 - Print raster bitmap
      bytes.addAll([29, 118, 48, 0]); // GS v 0 m xL xH yL yH d1...dk
      bytes.add(widthBytes % 256); // xL
      bytes.add(widthBytes ~/ 256); // xH
      bytes.add(1); // yL (1 line)
      bytes.add(0); // yH
      
      // Convert line to bitmap data
      for (int x = 0; x < widthBytes; x++) {
        int byte = 0;
        for (int bit = 0; bit < 8; bit++) {
          final pixelX = x * 8 + bit;
          if (pixelX < image.width) {
            final pixel = image.getPixel(pixelX, y);
            final luminance = (0.299 * pixel.r + 0.587 * pixel.g + 0.114 * pixel.b);
            if (luminance < 128) { // Dark pixel
              byte |= (1 << (7 - bit));
            }
          }
        }
        bytes.add(byte);
      }
    }
    
    // Reset line spacing
    bytes.addAll([27, 50]); // ESC 2 - Reset line spacing
    
    return bytes;
  }


  // Generate simple test receipt
  static Future<List<int>> _generateSimpleTestReceipt() async {
    List<int> bytes = [];

    // Initialize printer
   // bytes.addAll([27, 64]); // ESC @ - Initialize



  // Disable GB18030 and initialize for Arabic
    bytes.addAll([27, 64]); // ESC @ - Initialize
    bytes.addAll([28, 46]); // FS . - Cancel GB18030 mode
    bytes.addAll([27, 82, 0]); // ESC R 0 - International character set
    bytes.addAll([27, 116, 3]); // ESC t 3 - UTF-8 codepage
    bytes.addAll([28, 67, 2]); // FS C 2 - Arabic/Hebrew support
 
 

   // Header
     bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getMHTP29LArabicBytes('أختبار الطباعة : ', large: true, bold: true));
    bytes.addAll(_getMHTP29LArabicBytes('طباعة ناجحة : '));
    bytes.addAll(_getMHTP29LArabicBytes('================================'));
    bytes.addAll([10]);

/*

    // Header - Center aligned
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getSimpleTextBytes('TEST PRINT', bold: true, large: true));
    bytes.addAll(_getSimpleTextBytes('تطبيق التحصيل'));
    bytes.addAll(_getSimpleTextBytes('================================'));
    bytes.addAll([10]);

    // Content - Left aligned
    bytes.addAll([27, 97, 0]); // Left alignment
    bytes.addAll(_getSimpleTextBytes('System Test Print'));
    bytes.addAll(_getSimpleTextBytes('Date: ${_formatDateTime(DateTime.now())}'));
    bytes.addAll(_getSimpleTextBytes('Time: ${_formatTime(DateTime.now())}'));
    bytes.addAll([10]);
    bytes.addAll(_getSimpleTextBytes('================================'));
    bytes.addAll([10]);

    bytes.addAll(_getSimpleTextBytes('Printer Status: OK'));
    bytes.addAll(_getSimpleTextBytes('Connection: Bluetooth'));
    bytes.addAll(_getSimpleTextBytes('Numbers: 1234567890'));

    // Footer
    bytes.addAll([10]);
    bytes.addAll(_getSimpleTextBytes('================================'));
    bytes.addAll([27, 97, 1]); // Center alignment
    bytes.addAll(_getSimpleTextBytes('TEST SUCCESSFUL', bold: true));
    bytes.addAll([10, 10, 10]);
*/
    // Cut paper
    bytes.addAll([29, 86, 65, 3]);

    return bytes;
  }



  // Print collection receipt as PDF (simplified compact version for mobile)
  static Future<bool> printCollectionReceiptPDF(InvoiceReceipt invoiceReceipt) async {
    try {
      if (!_isConnected || _selectedPrinter == null) {
        if (kDebugMode) {
          print('No Bluetooth printer connected');
        }
        return false;
      }

      if (kDebugMode) {
        print('Creating compact PDF-style receipt for Bluetooth printer...');
      }

      // Create compact thermal printer commands with clean formatting
      List<int> bytes = [];

      // Initialize printer
      bytes.addAll([27, 64]); // ESC @ - Initialize
      bytes.addAll([27, 116, 3]); // UTF-8 codepage
      bytes.addAll([28, 67, 2]); // Arabic support

      // Header - Center aligned
      bytes.addAll([27, 97, 1]); // Center alignment
      bytes.addAll(_getMHTP29LArabicBytes('المؤسسة العامة للكهرباء', bold: true, large: true));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('إيصال تحصيل', bold: true));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('================================'));
      bytes.addAll([10]);

      // Transaction details - Left aligned
      bytes.addAll([27, 97, 0]); // Left alignment
      bytes.addAll(_getMHTP29LArabicBytes('رقم المعاملة: ${invoiceReceipt.transactionId ?? 'غير محدد'}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('رقم المشترك: ${invoiceReceipt.subscriberNumber ?? 'غير محدد'}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('اسم المشترك: ${invoiceReceipt.subscriberName ?? 'غير محدد'}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('رقم العداد: ${invoiceReceipt.meterNumber ?? 'غير محدد'}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('================================'));
      bytes.addAll([10]);

      // Payment amount - Center aligned and bold
      bytes.addAll([27, 97, 1]); // Center alignment
      bytes.addAll(_getMHTP29LArabicBytes('المبلغ المدفوع', bold: true));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي', bold: true, large: true));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('================================'));
      bytes.addAll([10]);

      // Print details - Left aligned
      bytes.addAll([27, 97, 0]); // Left alignment
      bytes.addAll(_getMHTP29LArabicBytes('طبع بواسطة: ${invoiceReceipt.userPrint ?? 'غير محدد'}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('تاريخ الطباعة: ${_formatDateTime(invoiceReceipt.datePrint)}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('وقت الطباعة: ${_formatTime(invoiceReceipt.datePrint)}'));
      bytes.addAll([10]);

      // Footer
      bytes.addAll([27, 97, 1]); // Center alignment
      bytes.addAll(_getMHTP29LArabicBytes('================================'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('شكراً لكم', bold: true));
      bytes.addAll([10, 10]);

      // Cut paper
      bytes.addAll([29, 86, 65, 3]);

      // Send to printer
      final result = await PrintBluetoothThermal.writeBytes(bytes);

      if (kDebugMode) {
        print('Bluetooth thermal printer compact PDF-style receipt result: $result');
      }

      return result;

      // Convert PDF to image for thermal printer
      if (kDebugMode) {
        print('Converting PDF to image for thermal printer...');
      }

      try {
        // Convert PDF to image using Printing.raster
        final pdfBytes = await pdf.save();

        // Convert PDF to raster image
        await for (final page in Printing.raster(
          pdfBytes,
          pages: [0], // First page only
          dpi: 203, // Thermal printer DPI (203 or 300)
        )) {
          // Convert image to thermal printer format
          final imageBytes = await _convertImageToThermalPrinter(page);

          if (imageBytes.isNotEmpty) {
            // Send image to printer
            final result = await PrintBluetoothThermal.writeBytes(imageBytes);

            if (kDebugMode) {
              print('Bluetooth thermal printer PDF image result: $result');
            }

            return result;
          }
        }

        // If image conversion fails, fall back to text-based printing
        if (kDebugMode) {
          print('PDF to image conversion failed, falling back to text printing...');
        }

      } catch (e) {
        if (kDebugMode) {
          print('Error converting PDF to image: $e');
          print('Falling back to text-based printing...');
        }
      }

      // Fallback: Convert PDF content to thermal printer text commands
      List<int> bytes = [];

      // Initialize printer
      bytes.addAll([27, 64]); // ESC @ - Initialize
      bytes.addAll([27, 97, 1]); // Center alignment

      // Header
      bytes.addAll([27, 97, 1]); // Center alignment
      bytes.addAll(_getMHTP29LArabicBytes('المؤسسة العامة للكهرباء', bold: true));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('إيصال تحصيل', bold: true));
      bytes.addAll([10, 10]);

      // Transaction details
      bytes.addAll(_getMHTP29LArabicBytes('رقم المعاملة: ${invoiceReceipt.transactionId ?? ''}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('رقم المشترك: ${invoiceReceipt.subscriberNumber ?? ''}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('اسم المشترك: ${invoiceReceipt.subscriberName ?? ''}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('رقم العداد: ${invoiceReceipt.meterNumber ?? ''}'));
      bytes.addAll([10, 10]);

      // Payment amount - centered and bold
      bytes.addAll([27, 97, 1]); // Center alignment
      bytes.addAll(_getMHTP29LArabicBytes('المبلغ المدفوع: ${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي', bold: true));
      bytes.addAll([10, 10]);

      // Print details
      bytes.addAll([27, 97, 0]); // Left alignment
      bytes.addAll(_getMHTP29LArabicBytes('طبع بواسطة: ${invoiceReceipt.userPrint ?? ''}'));
      bytes.addAll([10]);
      bytes.addAll(_getMHTP29LArabicBytes('طبع في: ${_formatDateTime(invoiceReceipt.datePrint)} - ${_formatTime(invoiceReceipt.datePrint)}'));
      bytes.addAll([10, 10, 10]);

      // Cut paper
      bytes.addAll([29, 86, 65, 3]);

      // Send to printer
      final result = await PrintBluetoothThermal.writeBytes(bytes);

      if (kDebugMode) {
        print('Bluetooth thermal printer PDF fallback result: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error printing PDF receipt on Bluetooth thermal printer: $e');
      }
      return false;
    }
  }

  // Convert PDF raster image to thermal printer format
  static Future<List<int>> _convertImageToThermalPrinter(PdfRaster page) async {
    try {
      // Convert PdfRaster to Image
      final imageBytes = await page.toPng();
      final image = img.decodeImage(imageBytes);

      if (image == null) {
        if (kDebugMode) {
          print('Failed to decode image');
        }
        return [];
      }

      // Resize image to fit thermal printer width (usually 384 pixels for 80mm paper)
      const thermalWidth = 384;
      final resizedImage = img.copyResize(
        image,
        width: thermalWidth,
        maintainAspect: true,
      );

      // Convert to monochrome (black and white)
      final monoImage = img.grayscale(resizedImage);

      // Convert to ESC/POS image commands
      List<int> bytes = [];

      // Initialize printer
      bytes.addAll([27, 64]); // ESC @ - Initialize
      bytes.addAll([27, 97, 1]); // Center alignment

      // Print image using ESC/POS raster bit image command
      // This is a simplified implementation - you might need to adjust based on your printer model

      final width = monoImage.width;
      final height = monoImage.height;

      // ESC * m nL nH d1...dk - Print raster bit image
      bytes.addAll([27, 42, 33]); // ESC * ! (24-dot double-density)
      bytes.addAll([width & 0xFF, (width >> 8) & 0xFF]); // Width in bytes

      // Convert image pixels to printer data
      for (int y = 0; y < height; y += 24) { // Process 24 lines at a time
        for (int x = 0; x < width; x++) {
          int byte1 = 0, byte2 = 0, byte3 = 0;

          // Process 24 vertical pixels
          for (int bit = 0; bit < 8; bit++) {
            if (y + bit < height) {
              final pixel = monoImage.getPixel(x, y + bit);
              final luminance = img.getLuminance(pixel);
              if (luminance < 128) { // Black pixel
                byte1 |= (1 << (7 - bit));
              }
            }
          }

          for (int bit = 0; bit < 8; bit++) {
            if (y + bit + 8 < height) {
              final pixel = monoImage.getPixel(x, y + bit + 8);
              final luminance = img.getLuminance(pixel);
              if (luminance < 128) { // Black pixel
                byte2 |= (1 << (7 - bit));
              }
            }
          }

          for (int bit = 0; bit < 8; bit++) {
            if (y + bit + 16 < height) {
              final pixel = monoImage.getPixel(x, y + bit + 16);
              final luminance = img.getLuminance(pixel);
              if (luminance < 128) { // Black pixel
                byte3 |= (1 << (7 - bit));
              }
            }
          }

          bytes.addAll([byte1, byte2, byte3]);
        }
        bytes.addAll([10]); // Line feed
      }

      // Add some space and cut paper
      bytes.addAll([10, 10, 10]);
      bytes.addAll([29, 86, 65, 3]); // Cut paper

      if (kDebugMode) {
        print('Image converted to thermal printer format: ${bytes.length} bytes');
      }

      return bytes;

    } catch (e) {
      if (kDebugMode) {
        print('Error converting image to thermal printer format: $e');
      }
      return [];
    }
  }

  // Alternative: Save PDF and open for printing (simpler approach)
  static Future<bool> printCollectionReceiptPDFDirect(InvoiceReceipt invoiceReceipt) async {
    try {
      await _loadArabicFonts();

      if (kDebugMode) {
        print('Creating PDF collection receipt for direct printing...');
      }

      // Create a PDF document for the collection receipt with thermal printer format
      final pdf = pw.Document();

      // Use thermal printer paper size (80mm width)
      const thermalFormat = PdfPageFormat(
        80 * PdfPageFormat.mm, // 80mm width
        double.infinity, // Auto height
        marginAll: 2 * PdfPageFormat.mm, // Small margins like thermal printer
      );

      pdf.addPage(
        pw.Page(
          pageFormat: thermalFormat,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header - Center aligned like thermal printer
                pw.Center(
                  child: pw.Text(
                    'المؤسسة العامة للكهرباء',
                    style: _createTextStyleWithFallback(fontSize: 14, isBold: true),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.Center(
                  child: pw.Text(
                    'إيصال تحصيل',
                    style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),

                pw.SizedBox(height: 4),

                // Transaction details
                _buildMinimalRow('رقم المعاملة:', invoiceReceipt.transactionId ?? ''),
                _buildMinimalRow('رقم المشترك:', invoiceReceipt.subscriberNumber ?? ''),
                _buildMinimalRow('اسم المشترك:', invoiceReceipt.subscriberName ?? ''),
                _buildMinimalRow('رقم العداد:', invoiceReceipt.meterNumber ?? ''),

                pw.SizedBox(height: 2),

                // Payment details
                pw.Center(
                  child: pw.Text(
                    'المبلغ المدفوع: ${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي',
                    style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
                    textAlign: pw.TextAlign.right,
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),

                pw.SizedBox(height: 2),
                _buildMinimalRow('طبع بواسطة:', ' ${invoiceReceipt.userPrint!} '),
                _buildMinimalRow('طبع في:', '${_formatDateTime(invoiceReceipt.datePrint)} - ${_formatTime(invoiceReceipt.datePrint)}'),

                pw.SizedBox(height: 6), // Extra space like thermal printer
              ],
            );
          },
        ),
      );

      // Use Printing.layoutPdf to open PDF for printing
      // This will open the system print dialog where user can select printer
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'إيصال_تحصيل_${invoiceReceipt.transactionId ?? DateTime.now().millisecondsSinceEpoch}',
        format: thermalFormat,
      );

      if (kDebugMode) {
        print('PDF opened for printing successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error opening PDF for printing: $e');
      }
      return false;
    }
  }
}
