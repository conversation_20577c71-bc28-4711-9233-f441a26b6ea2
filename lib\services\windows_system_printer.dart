import 'package:elecollect_app/models/invoice_receipt.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import '../models/collection_report.dart';
import '../models/user.dart';

class WindowsSystemPrinter {
  static bool _isConnected = false;
  static String? _selectedPrinterName;

  // Get available system printers
  static Future<List<String>> getAvailablePrinters() async {
    try {
      if (kDebugMode) {
        print('Getting Windows system printers...');
      }

      // Get available printers using the printing package
      final printers = await Printing.listPrinters();
      
      List<String> printerNames = printers.map((printer) => printer.name).toList();
      
      if (kDebugMode) {
        print('Found ${printerNames.length} system printers: $printerNames');
      }
      
      return printerNames;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting Windows system printers: $e');
      }
      return [];
    }
  }

  // Connect to a system printer (just select it)
  static Future<bool> connectToPrinter(String printerName) async {
    try {
      if (kDebugMode) {
        print('Selecting Windows system printer: $printerName');
      }

      // For system printers, "connecting" just means selecting the printer
      _selectedPrinterName = printerName;
      _isConnected = true;

      if (kDebugMode) {
        print('Windows system printer selected successfully: $printerName');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error selecting Windows system printer: $e');
      }
      _isConnected = false;
      _selectedPrinterName = null;
      return false;
    }
  }

  // Check connection status
  static bool isConnected() {
    return _isConnected && _selectedPrinterName != null;
  }

  // Disconnect (just clear selection)
  static Future<void> disconnect() async {
    try {
      _selectedPrinterName = null;
      _isConnected = false;
      
      if (kDebugMode) {
        print('Windows system printer disconnected');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error disconnecting Windows system printer: $e');
      }
    }
  }
    static pw.Font? _arabicFont;
  static pw.Font? _arabicBoldFont;
  static pw.Font? _fallbackFont;
  static pw.Font? _fallbackBoldFont;
  static pw.ImageProvider? _logoImage;
  
    static Future<void> _loadArabicFonts() async {
    if (_arabicFont == null || _arabicBoldFont == null) {
      try {
        // Load Google Fonts with Arabic support and fallback fonts
        final arabicRegular = await PdfGoogleFonts.notoSansArabicRegular();
        final arabicBold = await PdfGoogleFonts.notoSansArabicBold();
        final fallbackRegular = await PdfGoogleFonts.notoSansRegular();
        final fallbackBold = await PdfGoogleFonts.notoSansBold();

        _arabicFont = arabicRegular;
        _arabicBoldFont = arabicBold;
        _fallbackFont = fallbackRegular;
        _fallbackBoldFont = fallbackBold;
      } catch (e) {
        // Fallback to basic fonts
        _arabicFont = await PdfGoogleFonts.notoSansRegular();
       
        _arabicBoldFont = await PdfGoogleFonts.notoSansBold();
      }
    }

    // Load logo if not already loaded
     // Load logo if not already loaded
    if (_logoImage == null) {
      try {
        final logoData = await rootBundle.load('assets/images/peclogo.png');
        _logoImage = pw.MemoryImage(logoData.buffer.asUint8List());
      } catch (e) {
        // Logo loading failed, continue without logo
        _logoImage = null;
      }
    }
  }



    // Create TextStyle with comprehensive font fallback for Arabic
  static pw.TextStyle _createTextStyleWithFallback({
    required double fontSize,
    required bool isBold,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: isBold ? pw.FontWeight.bold : pw.FontWeight.normal,
      font: isBold ? _arabicBoldFont : _arabicFont,
      fontFallback: [
        // Arabic fonts
        if (_arabicBoldFont != null && _arabicFont != null) ...[
          isBold ? _arabicBoldFont! : _arabicFont!,
        ],
        // Fallback fonts
        if (_fallbackBoldFont != null && _fallbackFont != null) ...[
          isBold ? _fallbackBoldFont! : _fallbackFont!,
        ],
      ],
    );
  }

  
 // Test print
  static Future<bool> testPrint() async {
    try {

        await _loadArabicFonts();
      if (!_isConnected || _selectedPrinterName == null) {
        if (kDebugMode) {
          print('No Windows system printer selected');
        }
        return false;
      }

      if (kDebugMode) {
        print('Creating test print document for Windows system printer...');
      }

      // Create a PDF document for test printing with thermal format
      final pdf = pw.Document();

      // Use thermal printer paper size (80mm width)
      const thermalFormat = PdfPageFormat(
        80 * PdfPageFormat.mm, // 80mm width
        double.infinity, // Auto height
        marginAll: 2 * PdfPageFormat.mm, // Small margins like thermal printer
      );

      pdf.addPage(
        pw.Page(
          pageFormat: thermalFormat,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header - Center aligned like thermal printer
                pw.Center(
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'اختبار الطباعة',
                                  style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,

                  
                      
                      ),
                     /* pw.SizedBox(height: 2),
                      pw.Text(
                        'طباعة ناجحة',
                                        style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                      ),*/
                      pw.Text(
                        '================================',
                        style: pw.TextStyle(fontSize: 8),
                      ),

                       pw.Text(
                  'التاريخ: ${_formatDateTime(DateTime.now())}',
                                  style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الوقت: ${_formatTime(DateTime.now())}',
                                style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 2),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),

                    ],
                  ),
                ),
                pw.SizedBox(height: 4),
   pw.Center(
                  child: pw.Text(
                    'تم الاختبار بنجاح',
                                   style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                  ),
                ),
       
                pw.SizedBox(height: 3),
                // Test content - Left aligned like thermal printer
              /*  pw.Text(
                  'اختبار طباعة النظام',
                                 style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),*/
                /*pw.Text(
                  'الطابعة: $_selectedPrinterName',
                                 style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),*/
               
             /*   pw.SizedBox(height: 2),

                // Test Arabic text
                pw.Text(
                  'دعم اللغة العربية: نعم',
                                 style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الأرقام: ١٢٣٤٥٦٧٨٩٠',
                                 style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                ),
*/
                // Footer
               /* pw.SizedBox(height: 4),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),*/
              // Extra space like thermal printer
              ],
            );
          },
        ),
      );
      // Print the document
      final result = await Printing.directPrintPdf(
        printer: Printer(url: _selectedPrinterName!),
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );

      if (kDebugMode) {
        print('Windows system printer test print result: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error in Windows system printer test print: $e');
      }
      return false;
    }
  }
 

  // Test print

/*  static Future<bool> testPrint() async {
    try {
      if (!_isConnected || _selectedPrinterName == null) {
        if (kDebugMode) {
          print('No Windows system printer selected');
        }
        return false;
      }

      if (kDebugMode) {
        print('Creating test print document for Windows system printer...');
      }

      // Create a PDF document for test printing with thermal format
      final pdf = pw.Document();

      // Use thermal printer paper size (80mm width)
      const thermalFormat = PdfPageFormat(
        80 * PdfPageFormat.mm, // 80mm width
        double.infinity, // Auto height
        marginAll: 2 * PdfPageFormat.mm, // Small margins like thermal printer
      );

      pdf.addPage(
        pw.Page(
          pageFormat: thermalFormat,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header - Center aligned like thermal printer
                pw.Center(
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'اختبار الطباعة',
                        style: pw.TextStyle(
                          fontSize: 16, // Large like thermal printer
                          fontWeight: pw.FontWeight.bold,
                        ),
                        textDirection: pw.TextDirection.rtl,
                      ),
                      pw.SizedBox(height: 2),
                      pw.Text(
                        'تطبيق EleCollect',
                        style: pw.TextStyle(fontSize: 10),
                        textDirection: pw.TextDirection.rtl,
                      ),
                      pw.Text(
                        '================================',
                        style: pw.TextStyle(fontSize: 8),
                      ),
                    ],
                  ),
                ),
                pw.SizedBox(height: 4),

                // Test content - Left aligned like thermal printer
                pw.Text(
                  'اختبار طباعة النظام',
                  style: pw.TextStyle(fontSize: 9),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الطابعة: $_selectedPrinterName',
                  style: pw.TextStyle(fontSize: 9),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'التاريخ: ${_formatDateTime(DateTime.now())}',
                  style: pw.TextStyle(fontSize: 9),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الوقت: ${_formatTime(DateTime.now())}',
                  style: pw.TextStyle(fontSize: 9),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 2),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.SizedBox(height: 2),

                // Test Arabic text
                pw.Text(
                  'دعم اللغة العربية: نعم',
                  style: pw.TextStyle(fontSize: 9),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الأرقام: ١٢٣٤٥٦٧٨٩٠',
                  style: pw.TextStyle(fontSize: 9),
                  textDirection: pw.TextDirection.rtl,
                ),

                // Footer
                pw.SizedBox(height: 4),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.Center(
                  child: pw.Text(
                    'تم الاختبار بنجاح',
                    style: pw.TextStyle(
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold,
                    ),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.SizedBox(height: 6), // Extra space like thermal printer
              ],
            );
          },
        ),
      );

      // Print the document
      final result = await Printing.directPrintPdf(
        printer: Printer(url: _selectedPrinterName!),
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );

      if (kDebugMode) {
        print('Windows system printer test print result: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error in Windows system printer test print: $e');
      }
      return false;
    }
  }*/

  // Print collection receipt
  static Future<bool> printCollectionReceipt( 
     InvoiceReceipt invoiceReceipt
  //  CollectionReport collection,
   //  User user
     
     ) async {
    try {

        await _loadArabicFonts();


      if (!_isConnected || _selectedPrinterName == null) {
        if (kDebugMode) {
          print('No Windows system printer selected');
        }
        return false;
      }

      if (kDebugMode) {
        print('Creating collection receipt for Windows system printer...');
      }

      // Create a PDF document for the collection receipt with thermal printer format
      final pdf = pw.Document();

      // Use thermal printer paper size (80mm width)
    /*  const thermalFormat = PdfPageFormat(
        80 * PdfPageFormat.mm, // 80mm width
        double.infinity, // Auto height
        marginAll: 2 * PdfPageFormat.mm, // Small margins like thermal printer
      );
*/

        const thermalFormat = PdfPageFormat(
      75 * PdfPageFormat.mm, // 72mm width (optimized for safe printing)
     // 120 * PdfPageFormat.mm, // Fixed height for 80mm paper
        double.infinity, // Auto height
      marginAll: 1 * PdfPageFormat.mm, // Increased margins for 72mm width
    );

      pdf.addPage(
        pw.Page(
          pageFormat: thermalFormat,
              margin: const pw.EdgeInsets.all(10), // Increased margin for 72mm width

          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header - Center aligned like thermal printer
                pw.Center(
                  child: pw.Column(
                    children: [

                        // Logo at the top
                 if (_logoImage != null)
                  pw.Container(
                  height: 80, // Smaller logo for minimal receipt
                  child: pw.Image(_logoImage!),
                  ),
                     if (_logoImage != null) pw.SizedBox(height: 2),


                      pw.Text(
                        'سند تحصيل',
                    style: _createTextStyleWithFallback(fontSize: 16, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                      ),
                      pw.SizedBox(height: 1),
                      pw.Text(
                        'المؤسسة العامة للكهرباء',
                          style: _createTextStyleWithFallback(fontSize: 10, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                      ),
                      /*pw.Text(
                        '================================',
                        style: pw.TextStyle(fontSize: 8),
                      ),*/
                    ],
                  ),
                ),
                pw.SizedBox(height: 2), 

                // Transaction details - Left aligned like thermal printer
               _buildMinimalRow('رقم السند:', ' ${invoiceReceipt.docId!.isNotEmpty ? invoiceReceipt.docId : 'غير محدد'}'),
   _buildMinimalRow('تاريخ التحصيل :', '${_formatDateTime(invoiceReceipt.dateInvoice)}'),
    
    //  _buildMinimalRow('الوقت:', '${_formatTime(collection.transactionDate)} '),

            /*    pw.Text(
                  'رقم السند: ${collection.transactionId.isNotEmpty ? collection.transactionId : 'غير محدد'}',
                       style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'التاريخ: ${_formatDateTime(collection.transactionDate)}',
                    style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الوقت: ${_formatTime(collection.transactionDate)}',
                 style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),*/
               /* pw.SizedBox(height: 2),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.SizedBox(height: 2),*/

  // _buildMinimalRow('رقم المشترك:', '${invoiceReceipt.subscriberNumber}' ),
      _buildMinimalRow('اسم المشترك:', '${invoiceReceipt.subscriberName}'),
     //    _buildMinimalRow('رقم العداد:', '${invoiceReceipt.meterNumber!}'),

                // Subscriber details
              /*  pw.Text(
                  'رقم الاشتراك: ${collection.subscriberNumber}',
                 style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'اسم المشترك: ${collection.subscriberName}',
                     style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                if (collection.meterNumber != null && collection.meterNumber!.isNotEmpty)
                  pw.Text(
                    'رقم العداد: ${collection.meterNumber!}',
                       style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                  ),*/

                /*pw.SizedBox(height: 2),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.SizedBox(height: 2),*/

   _buildMinimalRow('المبلغ المستحق:', '${invoiceReceipt.dueAmount!.toStringAsFixed(2)} ر.ي'),
      //_buildMinimalRow('طريقة الدفع:', '${_getPaymentMethodArabic(invoiceReceipt.paymentType)}'),
         _buildMinimalRow('اسم المحصل :', ' ${invoiceReceipt.collector!} '),
         
                // Payment details
               /* pw.Text(
                  'المبلغ المستحق: ${collection.amountDue.toStringAsFixed(2)} ر.ي',
             style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),*/
                // Amount paid - Center aligned and bold like thermal printer
                pw.Center(
                  child: pw.Text(
                    'المبلغ المدفوع: ${invoiceReceipt.paidAmount.toStringAsFixed(2)} ر.ي',
                style: _createTextStyleWithFallback(fontSize: 12, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                  ),
                ),
pw.SizedBox(height: 2),
                  _buildMinimalRow('طبع بواسطة :', ' ${invoiceReceipt.userPrint!} '),
                  _buildMinimalRow('طبع في     :', '${_formatDateTime(invoiceReceipt.datePrint)}' + ' _ ' +  '${_formatTime(invoiceReceipt.datePrint)}'),
                 
              /*  pw.Text(
                  'طريقة الدفع: ${_getPaymentMethodArabic(collection.paymentMethod)}',
                        style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'الرصيد الجديد: ${collection.newBalance.toStringAsFixed(2)} ر.ي',
                         style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                ),
*/
                // Notes if available
               /* if (invoiceReceipt.notes != null && invoiceReceipt.notes!.isNotEmpty) ...[
                  pw.SizedBox(height: 2),
                  pw.Text(
                    'ملاحظات: ${invoiceReceipt.notes!}',
                  style: _createTextStyleWithFallback(fontSize: 9, isBold: false),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
                  ),
                ],*/

                // Footer
                /*pw.SizedBox(height: 4),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.Center(
                  child: pw.Text(
                    'شكراً لكم',
                    style: _createTextStyleWithFallback(fontSize: 10, isBold: true),
              textAlign: pw.TextAlign.left,
              textDirection: pw.TextDirection.rtl,
                  ),
                ),*/
                pw.SizedBox(height: 6), // Extra space like thermal printer
              ],
            );
          },
        ),
      );

      // Print the document
      final result = await Printing.directPrintPdf(
        printer: Printer(url: _selectedPrinterName!),
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );

      if (kDebugMode) {
        print('Windows system printer collection receipt result: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error printing collection receipt on Windows system printer: $e');
      }
      return false;
    }
  }


  static Future<bool> printCollectionReceiptTest( ) async {
    try {

        await _loadArabicFonts();


      if (!_isConnected || _selectedPrinterName == null) {
        if (kDebugMode) {
          print('No Windows system printer selected');
        }
        return false;
      }

      if (kDebugMode) {
        print('Creating collection receipt for Windows system printer...');
      }

      // Create a PDF document for the collection receipt with thermal printer format
      final pdf = pw.Document();

      // Use thermal printer paper size (80mm width)
    /*  const thermalFormat = PdfPageFormat(
        80 * PdfPageFormat.mm, // 80mm width
        double.infinity, // Auto height
        marginAll: 2 * PdfPageFormat.mm, // Small margins like thermal printer
      );
*/

        const thermalFormat = PdfPageFormat(
      75 * PdfPageFormat.mm, // 72mm width (optimized for safe printing)
     // 120 * PdfPageFormat.mm, // Fixed height for 80mm paper
        double.infinity, // Auto height
      marginAll: 1 * PdfPageFormat.mm, // Increased margins for 72mm width
    );

      pdf.addPage(
        pw.Page(
          pageFormat: thermalFormat,
              margin: const pw.EdgeInsets.all(10), // Increased margin for 72mm width

          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header - Center aligned like thermal printer
                pw.Center(
                  child: pw.Column(
                    children: [

                        // Logo at the top
                 if (_logoImage != null)
                  pw.Container(
                  height: 80, // Smaller logo for minimal receipt
                  child: pw.Image(_logoImage!),
                  ),
                     if (_logoImage != null) pw.SizedBox(height: 2),


                      pw.Text(
                        'سند تحصيل',
                    style: _createTextStyleWithFallback(fontSize: 16, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                      ),
                      pw.SizedBox(height: 2),
                      pw.Text(
                        'المؤسسة العامة للكهرباء',
                          style: _createTextStyleWithFallback(fontSize: 10, isBold: true),
          textAlign: pw.TextAlign.center,
          textDirection: pw.TextDirection.rtl,
                      ),
                      pw.Text(
                        '================================',
                        style: pw.TextStyle(fontSize: 8),
                      ),
                    ],
                  ),
                ),
                pw.SizedBox(height: 4),

                // Transaction details - Left aligned like thermal printer
               _buildMinimalRow('رقم السند:', ' ${  'غير محدد'}'),
   _buildMinimalRow('التاريخ:', '${ 'غير محدد'}'),
 
 
                pw.SizedBox(height: 4),
                pw.Text(
                  '================================',
                  style: pw.TextStyle(fontSize: 8),
                ),
                pw.Center(
                  child: pw.Text(
                    'شكراً لكم',
                    style: _createTextStyleWithFallback(fontSize: 10, isBold: true),
              textAlign: pw.TextAlign.left,
              textDirection: pw.TextDirection.rtl,
                  ),
                ),
                pw.SizedBox(height: 6), // Extra space like thermal printer
              ],
            );
          },
        ),
      );

      // Print the document
      final result = await Printing.directPrintPdf(
        printer: Printer(url: _selectedPrinterName!),
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );

      if (kDebugMode) {
        print('Windows system printer collection receipt result: $result');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('Error printing collection receipt on Windows system printer: $e');
      }
      return false;
    }
  }



   // Minimal row for direct printing (72mm - optimized for safe printing)
  static pw.Widget _buildMinimalRow(String label, String value, {bool isBold = false}) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 1),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              value,
              style: _createTextStyleWithFallback(fontSize: 9, isBold: isBold),
              textAlign: pw.TextAlign.center,
              textDirection: label=='رقم السند:' ? pw.TextDirection.ltr : pw.TextDirection.rtl,
            ),
          ),
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              label,
              style: _createTextStyleWithFallback(fontSize: 9, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }


  static pw.Widget _buildMinimalRowSmall(String label, String value, {bool isBold = false}) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.symmetric(vertical: 1),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              value,
              style: _createTextStyleWithFallback(fontSize: 6, isBold: isBold),
              textAlign: pw.TextAlign.center,
              textDirection: label=='رقم السند:' ? pw.TextDirection.ltr : pw.TextDirection.rtl,
            ),
          ),
          pw.Expanded(
            flex: 1,
            child: pw.Text(
              label,
              style: _createTextStyleWithFallback(fontSize: 6, isBold: true),
              textAlign: pw.TextAlign.right,
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for formatting (matching thermal printer format)
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  static String _getPaymentMethodArabic(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'نقداً';
            case 'inbound':
        return 'نقداً';
      case 'card':
        return 'بطاقة';
      case 'transfer':
        return 'تحويل';
      default:
        return method;
    }
  }
}
