import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../models/collection_report.dart';

class ExcelExportService {
  static Future<String?> exportCollectionReport({
    required List<CollectionReport> collections,
    required CollectionReportSummary? summary,
    String? subscriberFilter,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // For now, we'll create a CSV file instead of Excel
      // This is simpler and works better with Arabic text

      final csvContent = _generateCSVContent(
        collections,
        summary,
        subscriberFilter,
        fromDate,
        toDate,
      );

      return await _saveCSVFile(csvContent, 'تقرير_التحصيلات');
    } catch (e) {
      print('Error exporting report: $e');
      return null;
    }
  }

  static String _generateCSVContent(
    List<CollectionReport> collections,
    CollectionReportSummary? summary,
    String? subscriberFilter,
    DateTime? fromDate,
    DateTime? toDate,
  ) {
    final buffer = StringBuffer();

    // Add header information
    buffer.writeln('تقرير التحصيلات');
    buffer.writeln('');

    if (subscriberFilter != null && subscriberFilter.isNotEmpty) {
      buffer.writeln('المشترك:,$subscriberFilter');
    } else {
      buffer.writeln('المشترك:,جميع المشتركين');
    }

    if (fromDate != null) {
      buffer.writeln('من تاريخ:,${_formatDate(fromDate)}');
    }

    if (toDate != null) {
      buffer.writeln('إلى تاريخ:,${_formatDate(toDate)}');
    }

    buffer.writeln('تاريخ التقرير:,${_formatDate(DateTime.now())}');
    buffer.writeln('');

    // Add summary if available
    if (summary != null) {
      buffer.writeln('ملخص التقرير');
      buffer.writeln('إجمالي المعاملات:,${summary.totalTransactions}');
      buffer.writeln('إجمالي المبلغ المستحق:,${summary.totalAmountDue.toStringAsFixed(2)} ريال');
      buffer.writeln('إجمالي المبلغ المدفوع:,${summary.totalAmountPaid.toStringAsFixed(2)} ريال');
      buffer.writeln('إجمالي المتبقي:,${summary.totalOutstanding.toStringAsFixed(2)} ريال');
      buffer.writeln('');
    }

    // Add data headers
    buffer.writeln('رقم المعاملة,رقم الاشتراك,اسم المشترك,العنوان,رقم العداد,المبلغ المستحق,المبلغ المدفوع,طريقة الدفع,الرصيد الجديد,التاريخ,الوقت,المحصل,ملاحظات');

    // Add data rows
    for (final collection in collections) {
      buffer.writeln([
        collection.transactionId,
        collection.subscriberNumber,
        collection.subscriberName,
        collection.address ?? '',
        collection.meterNumber ?? '',
        collection.amountDue.toStringAsFixed(2),
        collection.amountPaid.toStringAsFixed(2),
        collection.paymentMethodArabic,
        collection.newBalance.toStringAsFixed(2),
        collection.formattedDate,
        collection.formattedTime,
        collection.collectorName,
        collection.notes ?? '',
      ].map((field) => '"${field.toString().replaceAll('"', '""')}"').join(','));
    }

    return buffer.toString();
  }

  static String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  static Future<String> _saveCSVFile(String content, String fileName) async {
    if (kIsWeb) {
      // For web, we'll return a message indicating web download
      return 'web_download_ready';
    } else {
      // For mobile/desktop
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final file = File('${directory.path}/${fileName}_$timestamp.csv');
      await file.writeAsString(content, encoding: utf8);
      return file.path;
    }
  }
}
