import 'dart:io';
import 'dart:convert';
import 'package:elecollect_app/profile/app_info.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../models/collection_report.dart';

import 'package:syncfusion_flutter_xlsio/xlsio.dart' hide Row;

import '../helper/save_file_mobile.dart'
    if (dart.library.html) '../helper/save_file_web.dart';

//import 'package:smartoneexdemo/reports/pdf_go/save_file_mobile.dart'
 //   if (dart.library.html) 'package:smartoneexdemo/reports/pdf_go/save_file_web.dart';

class ExcelExportService {
  static Future<String?> exportCollectionReport({
    required List<BillingsRec> collections,
    required CollectionReportSummary? summary,
    String? subscriberFilter,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // For now, we'll create a CSV file instead of Excel
      // This is simpler and works better with Arabic text

    /*  final csvContent =await  _exportToExcel(
        collections,
        summary,
        subscriberFilter,
        fromDate,
        toDate,
      );

      return await _saveCSVFile(csvContent, 'تقرير_التحصيلات');
*/

            final Exported =await  _exportToExcel(
        collections,
        summary,
        subscriberFilter,
        fromDate,
        toDate,
      );

return Exported;

    } catch (e) {
      print('Error exporting report: $e');
      return null;
    }
  }
/*
  static String _generateCSVContent(
    List<BillingsRec> collections,
    CollectionReportSummary? summary,
    String? subscriberFilter,
    DateTime? fromDate,
    DateTime? toDate,
  ) {
    final buffer = StringBuffer();

    // Add header information
    buffer.writeln('تقرير التحصيلات');
    buffer.writeln('');

    if (subscriberFilter != null && subscriberFilter.isNotEmpty) {
      buffer.writeln('المشترك:,$subscriberFilter');
    } else {
      buffer.writeln('المشترك:,جميع المشتركين');
    }

    if (fromDate != null) {
      buffer.writeln('من تاريخ:,${_formatDate(fromDate)}');
    }

    if (toDate != null) {
      buffer.writeln('إلى تاريخ:,${_formatDate(toDate)}');
    }

    buffer.writeln('تاريخ التقرير:,${_formatDate(DateTime.now())}');
    buffer.writeln('');

    // Add summary if available
    if (summary != null) {
      buffer.writeln('ملخص التقرير');
      buffer.writeln('إجمالي المعاملات:,${summary.totalTransactions}');
      buffer.writeln('إجمالي المبلغ المستحق:,${summary.totalAmountDue.toStringAsFixed(2)} ريال');
      buffer.writeln('إجمالي المبلغ المدفوع:,${summary.totalAmountPaid.toStringAsFixed(2)} ريال');
      buffer.writeln('إجمالي المتبقي:,${summary.totalOutstanding.toStringAsFixed(2)} ريال');
      buffer.writeln('');
    }

    // Add data headers
    buffer.writeln('رقم المعاملة,رقم الاشتراك,اسم المشترك,العنوان,رقم العداد,المبلغ المستحق,المبلغ المدفوع,طريقة الدفع,الرصيد الجديد,التاريخ,الوقت,المحصل,ملاحظات');

    // Add data rows
    for (final collection in collections) {
      buffer.writeln([
        collection.transactionId,
        collection.subscriberNumber,
        collection.subscriberName,
        collection.address ?? '',
        collection.meterNumber ?? '',
        collection.amountDue.toStringAsFixed(2),
        collection.amountPaid.toStringAsFixed(2),
        collection.paymentMethodArabic,
        collection.newBalance.toStringAsFixed(2),
        collection.formattedDate,
        collection.formattedTime,
        collection.collectorName,
        collection.notes ?? '',
      ].map((field) => '"${field.toString().replaceAll('"', '""')}"').join(','));
    }

    return buffer.toString();
  }


*/
  static String _formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  static Future<String> _saveCSVFile(String content, String fileName) async {
    if (kIsWeb) {
      // For web, we'll return a message indicating web download
      return 'web_download_ready';
    } else {
      // For mobile/desktop
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final file = File('${directory.path}/${fileName}_$timestamp.csv');
      await file.writeAsString(content, encoding: utf8);
      return file.path;
    }
  }




 static Future<String?> _exportToExcel(

    List<BillingsRec> collections,
    CollectionReportSummary? summary,
    String? subscriberFilter,
    DateTime? fromDate,
    DateTime? toDate,

  ) async {
 
     try {
    //Creating a workbook.
    final Workbook workbook = Workbook();
    //Accessing via index
    final Worksheet sheet = workbook.worksheets[0];
    sheet.showGridlines = false;

    // Enable calculation for worksheet.
    sheet.enableSheetCalculations();

    //Set data in the worksheet.
    sheet.getRangeByName('A1').columnWidth = 4.82;
    sheet.getRangeByName('B1:C1').columnWidth = 13.82;
    sheet.getRangeByName('D1').columnWidth = 13.20;
    sheet.getRangeByName('E1').columnWidth = 7.50;
    sheet.getRangeByName('F1').columnWidth = 9.73;
    sheet.getRangeByName('G1').columnWidth = 8.82;
    sheet.getRangeByName('H1').columnWidth = 4.46;

    sheet.getRangeByName('A1:H1').cellStyle.backColor = '#333F4F';
    sheet.getRangeByName('A1:H1').merge();
    sheet.getRangeByName('B4:G6').merge();

    sheet.getRangeByName('B4').setText(AppInfo.vAppName);
    sheet.getRangeByName('B4').cellStyle.fontSize = 32;

    sheet.getRangeByName('B8').setText("تقرير التحصيلات");
    sheet.getRangeByName('B8').cellStyle.fontSize = 9;
    sheet.getRangeByName('B8').cellStyle.bold = true;

    sheet.getRangeByName('B8:G8').merge();

   // sheet.getRangeByName('B9').setText(vReportSubTitle);
    sheet.getRangeByName('B9').cellStyle.fontSize = 12;

    sheet.getRangeByName('B9:G9').merge();

  //  sheet.getRangeByName('B10').setText(vReportFromTo);
    sheet.getRangeByName('B10').cellStyle.fontSize = 9;
    sheet.getRangeByName('B10:G10').merge();
    // sheet.getRangeByName('B11').setText('9920 BridgePointe Parkway,');
    // sheet.getRangeByName('B11').cellStyle.fontSize = 9;

    //   sheet.getRangeByName('B12').setNumber(9365550136);
    // sheet.getRangeByName('B12').cellStyle.fontSize = 9;
    //  sheet.getRangeByName('B12').cellStyle.hAlign = HAlignType.left;

    final Range range1 = sheet.getRangeByName('F8:G8');
    final Range range2 = sheet.getRangeByName('F9:G9');
    final Range range3 = sheet.getRangeByName('F10:G10');
    final Range range4 = sheet.getRangeByName('F11:G11');
    final Range range5 = sheet.getRangeByName('F12:G12');

    // range1.merge();
    // range2.merge();
    // range3.merge();
    // range4.merge();
    // range5.merge();

    // sheet.getRangeByName('F8').setText('INVOICE#');
    range1.cellStyle.fontSize = 8;
    range1.cellStyle.bold = true;
    range1.cellStyle.hAlign = HAlignType.center;

    // sheet.getRangeByName('F9').setNumber(2058557939);
    range2.cellStyle.fontSize = 9;
    range2.cellStyle.hAlign = HAlignType.center;

    // sheet.getRangeByName('F10').setText('DATE');
    range3.cellStyle.fontSize = 8;
    range3.cellStyle.bold = true;
    range3.cellStyle.hAlign = HAlignType.center;

    //sheet.getRangeByName('F11').dateTime = DateTime(2020, 08, 31);
    // sheet.getRangeByName('F11').numberFormat =
    //    r'[$-x-sysdate]dddd, mmmm dd, yyyy';
    range4.cellStyle.fontSize = 9;
    range4.cellStyle.hAlign = HAlignType.center;

    range5.cellStyle.fontSize = 8;
    range5.cellStyle.bold = true;
    range5.cellStyle.hAlign = HAlignType.center;

    final Range range6 = sheet.getRangeByName('B15:H15');
    range6.cellStyle.fontSize = 10;
    range6.cellStyle.bold = true;

    sheet.getRangeByIndex(15, 2).setText('رقم السند');
    /* sheet.getRangeByIndex(16, 2).setText('CA-1098');
    sheet.getRangeByIndex(17, 2).setText('LJ-0192');
    sheet.getRangeByIndex(18, 2).setText('So-B909-M');
    sheet.getRangeByIndex(19, 2).setText('FK-5136');
    sheet.getRangeByIndex(20, 2).setText('HL-U509');*/

    sheet.getRangeByIndex(15, 3).setText('التاريخ');
    /*sheet.getRangeByIndex(16, 3).setText('CA-1098');
    sheet.getRangeByIndex(17, 3).setText('LJ-0192');
    sheet.getRangeByIndex(18, 3).setText('So-B909-M');
    sheet.getRangeByIndex(19, 3).setText('FK-5136');
    sheet.getRangeByIndex(20, 3).setText('HL-U509');*/

    sheet.getRangeByIndex(15, 4).setText('اسم المشترك');
    /*sheet.getRangeByIndex(16, 4).setText('AWC Logo Cap');
    sheet.getRangeByIndex(17, 4).setText('Long-Sleeve Logo Jersey, M');
    sheet.getRangeByIndex(18, 4).setText('Mountain Bike Socks, M');
    sheet.getRangeByIndex(19, 4).setText('ML Fork');
    sheet.getRangeByIndex(20, 4).setText('Sports-100 Helmet, Black');*/

    sheet.getRangeByIndex(15, 4, 15, 5).merge();
    /* sheet.getRangeByIndex(16, 4, 16, 5).merge();
    sheet.getRangeByIndex(17, 4, 17, 5).merge();
    sheet.getRangeByIndex(18, 4, 18, 5).merge();
    sheet.getRangeByIndex(19, 4, 19, 5).merge();
    sheet.getRangeByIndex(20, 4, 20, 5).merge();*/

    sheet.getRangeByIndex(15, 6).setText('طريقة الدفع');
    /*sheet.getRangeByIndex(16, 6).setNumber(2);
    sheet.getRangeByIndex(17, 6).setNumber(3);
    sheet.getRangeByIndex(18, 6).setNumber(2);
    sheet.getRangeByIndex(19, 6).setNumber(6);
    sheet.getRangeByIndex(20, 6).setNumber(1);*/

    sheet.getRangeByIndex(15, 7).setText('المحصل');
    /*sheet.getRangeByIndex(16, 7).setNumber(8.99);
    sheet.getRangeByIndex(17, 7).setNumber(49.99);
    sheet.getRangeByIndex(18, 7).setNumber(9.50);
    sheet.getRangeByIndex(19, 7).setNumber(175.49);
    sheet.getRangeByIndex(20, 7).setNumber(34.99);*/

    sheet.getRangeByIndex(15, 8).setText('المبلغ المدفوع');
    /*sheet.getRangeByIndex(16, 8).setFormula('=E16*F16+(E16*F16)');
    sheet.getRangeByIndex(17, 8).setFormula('=E17*F17+(E17*F17)');
    sheet.getRangeByIndex(18, 8).setFormula('=E18*F18+(E18*F18)');
    sheet.getRangeByIndex(19, 8).setFormula('=E19*F19+(E19*F19)');
    sheet.getRangeByIndex(20, 8).setFormula('=E20*F20+(E20*F20)');*/
    //sheet.getRangeByIndex(15, 7, 20, 8).numberFormat = r'$#,##0.00';

    // sheet.getRangeByIndex(15, 9).setText('اسم العميل');
    /*sheet.getRangeByIndex(16, 8).setFormula('=E16*F16+(E16*F16)');
    sheet.getRangeByIndex(17, 8).setFormula('=E17*F17+(E17*F17)');
    sheet.getRangeByIndex(18, 8).setFormula('=E18*F18+(E18*F18)');
    sheet.getRangeByIndex(19, 8).setFormula('=E19*F19+(E19*F19)');
    sheet.getRangeByIndex(20, 8).setFormula('=E20*F20+(E20*F20)');*/
    //sheet.getRangeByIndex(15, 7, 20, 8).numberFormat = r'$#,##0.00';

    // sheet.getRangeByName('E15:H15').cellStyle.hAlign = HAlignType.right;
    //  sheet.getRangeByName('B15:H15').cellStyle.fontSize = 10;
    // sheet.getRangeByName('B15:H15').cellStyle.bold = true;
    // sheet.getRangeByName('B16:H20').cellStyle.fontSize = 9;

    // sheet.getRangeByName('E22:H22').merge();
    // sheet.getRangeByName('E22:H22').cellStyle.hAlign = HAlignType.right;
    // sheet.getRangeByName('E23:H24').merge();

    String vColValue1 = "";
    String vColValue2 = "";
    String vColValue3 = "";
    String vColValue4 = "";
    String vColValue5 = "";
    String vColValue6 = "";
    String vColValue7 = "";
    int vcount = 1;
    int vcountR = 0;
   
    //for (Map<String, dynamic> row in widget.ReportRowsData) {
     for (final collection in collections) {
      vcount = 1;
      final List<String> valueTexts = [];

     /* for (final name in row.keys.toList()) {
        final column = _findColumn(name);
        String text = row[name].toString();
        if (column?.filterText != null) {
          text = column!.filterText!(row[name]);
        }
        valueTexts.add(text);

        if (vcount == 2) vColValue1 = text;
        if (vcount == 3) vColValue2 = text;
        if (vcount == 4) vColValue3 = text;
        if (vcount == 5) vColValue4 = text;
        if (vcount == 6) vColValue5 = text;
        if (vcount == 7) vColValue3 = vColValue3 + " - " + text;
        if (vcount == 8) vColValue6 = text;
        if (vcount == 9) {
          vColValue7 = text;
          sheet.getRangeByIndex(15, 9).setText('اسم العميل');
        }
        vcount = vcount + 1;
      }*/
vColValue1 = collection.id!;
vColValue2 =   _formatDateTime(collection.dateInvoice!) ;
vColValue3 = collection.partnerId[1];
vColValue4 = _getPaymentMethodArabic(collection.paymentType!);
vColValue5 = collection.writeUid[1];
vColValue6 = collection.amountTotal!.toStringAsFixed(2);


      if (vColValue1 != "------" && vColValue1 != "") {
        vcountR = vcountR + 1;
       double vtotald = double.parse(vColValue6);

        print(vcountR);
        sheet.getRangeByIndex(15 + vcountR, 2).setText(vColValue1);
        sheet.getRangeByIndex(15 + vcountR, 3).setText(vColValue2);
        sheet.getRangeByIndex(15 + vcountR, 4).setText(vColValue3);
        sheet.getRangeByIndex(15 + vcountR, 6).setText(vColValue4);
        sheet.getRangeByIndex(15 + vcountR, 7).setText(vColValue5);
        sheet.getRangeByIndex(15 + vcountR, 8).setNumber(vtotald);
         
      } else {
        break;
      }
    }

    final Range range7 = sheet.getRangeByIndex(15 + vcountR + 2, 7);
    final Range range8 = sheet.getRangeByIndex(15 + vcountR + 2, 8);
  //  range7.setText('الاجمالي');
    range7.cellStyle.fontSize = 10;
    range7.cellStyle.bold = true;

    int lastr = 15 + vcountR;

   range8.setFormula('=SUM(H16:H' + lastr.toString() + ')');
     range8.numberFormat = r'#,##0.00';
    range8.cellStyle.fontSize = 10;
    range8.cellStyle.hAlign = HAlignType.center;
    range8.cellStyle.bold = true;
/*
    sheet.getRangeByIndex(26, 1).text =
        '800 Interchange Blvd, Suite 2501, Austin, TX 78721 | <EMAIL>';
    sheet.getRangeByIndex(26, 1).cellStyle.fontSize = 8;

    final Range range9 = sheet.getRangeByName('A26:H27');
    range9.cellStyle.backColor = '#ACB9CA';
    range9.merge();
    range9.cellStyle.hAlign = HAlignType.center;
    range9.cellStyle.vAlign = VAlignType.center;
*/
    //Save and launch the excel.
    final List<int> bytes = workbook.saveAsStream();
    //Dispose the document.
    workbook.dispose();

    //Save and launch the file.
    //await saveAndLaunchFile(bytes, 'Invoice.xlsx');
    await FileSaveHelper.saveAndLaunchFile(bytes, 'Invoice.xlsx');
     return "Done";
       } catch (e) {

        return null;
      print('Error exporting report: $e');
      
    }
  }
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
  static String _getPaymentMethodArabic(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'نقداً';
            case 'inbound':
        return 'نقداً';
      case 'card':
        return 'بطاقة';
      case 'transfer':
        return 'تحويل';
      default:
        return method;
    }
  }

}
