class InvoiceReceipt {
  final String? docId;
  final String? transactionId;
  final String? displayName;

  final String? subscriberNumber;
  final String? subscriberName;
    final String? meterNumber;

  final double? dueAmount;
  final double paidAmount;
    final double? newBalance;

  final DateTime dateInvoice;
  final DateTime datePrint;

  final String paymentType;
  final String? currency;

  final String? collector;
    final String? userPrint;
  final String? notes;

  InvoiceReceipt({
    required this.docId,
    required this.transactionId,
    required this.displayName,
    required this.subscriberNumber,
    required this.subscriberName,
    this.meterNumber ,
    required this.dueAmount,
    required this.paidAmount,
        this.newBalance ,
    required this.dateInvoice,
    required this.datePrint,
    required this.paymentType,
    required this.currency,
    required this.collector,
        required this.userPrint,
    required this.notes,
  });
}

// Subscriber is imported from user.dart
