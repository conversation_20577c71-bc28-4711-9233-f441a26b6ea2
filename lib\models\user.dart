class User {
  final int id;
  final String username;
  final String fullName;
  final List<String> permissions;

  User({
    required this.id,
    required this.username,
    required this.fullName,
    required this.permissions,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      fullName: json['fullName'] ?? '',
      permissions: List<String>.from(json['permissions'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'fullName': fullName,
      'permissions': permissions,
    };
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }
}

class LoginRequest {
  final String username;
  final String password;

  LoginRequest({
    required this.username,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'password': password,
    };
  }
}

class LoginResponse {
  final bool success;
  final String message;
  final User? user;
  final List<Subscriber>? subscribers;

  LoginResponse({
    required this.success,
    required this.message,
    this.user,
    this.subscribers,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      subscribers: json['subscribers'] != null
          ? List<Subscriber>.from(
              json['subscribers'].map((x) => Subscriber.fromJson(x)))
          : null,
    );
  }
}

class Subscriber {
  final int id;
  final String subscriberNumber;
  final String subscriberName;
  final String? address;
  final String? phone;
  final String? meterNumber;
  final double currentBalance;
  final double lastReading;
  final DateTime? lastReadingDate;

  Subscriber({
    required this.id,
    required this.subscriberNumber,
    required this.subscriberName,
    this.address,
    this.phone,
    this.meterNumber,
    required this.currentBalance,
    required this.lastReading,
    this.lastReadingDate,
  });

  factory Subscriber.fromJson(Map<String, dynamic> json) {
    return Subscriber(
      id: json['id'] ?? 0,
      subscriberNumber: json['subscriberNumber'] ?? '',
      subscriberName: json['subscriberName'] ?? '',
      address: json['address'],
      phone: json['phone'],
      meterNumber: json['meterNumber'],
      currentBalance: (json['currentBalance'] ?? 0).toDouble(),
      lastReading: (json['lastReading'] ?? 0).toDouble(),
      lastReadingDate: json['lastReadingDate'] != null
          ? DateTime.parse(json['lastReadingDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'subscriberNumber': subscriberNumber,
      'subscriberName': subscriberName,
      'address': address,
      'phone': phone,
      'meterNumber': meterNumber,
      'currentBalance': currentBalance,
      'lastReading': lastReading,
      'lastReadingDate': lastReadingDate?.toIso8601String(),
    };
  }
}
