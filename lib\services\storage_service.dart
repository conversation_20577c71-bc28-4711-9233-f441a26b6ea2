import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'api_service.dart';

class StorageService {
  static const String _userKey = 'user_data';
  static const String _subscribersKey = 'subscribers_data';
  static const String _usernameKey = 'saved_username';
  static const String _rememberUsernameKey = 'remember_username';
  static const String _authTokenKey = 'auth_token';

  // حفظ بيانات المستخدم
  static Future<void> saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
  }

  // جلب بيانات المستخدم
  static Future<User?> getUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userKey);

      if (userData == null) return null;

      final userMap = jsonDecode(userData) as Map<String, dynamic>;
      return User.fromJson(userMap);
    } catch (e) {
      return null;
    }
  }

  // حفظ التوكن
  static Future<void> saveAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_authTokenKey, token);
    ApiService.setAuthToken(token);
  }

  // جلب التوكن
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_authTokenKey);
    if (token != null) {
      ApiService.setAuthToken(token);
    }
    return token;
  }

  // حفظ قائمة المشتركين
  static Future<void> saveSubscribers(List<Subscriber> subscribers) async {
    final prefs = await SharedPreferences.getInstance();
    final subscribersJson = subscribers.map((s) => s.toJson()).toList();
    await prefs.setString(_subscribersKey, jsonEncode(subscribersJson));
  }

  // جلب قائمة المشتركين
  static Future<List<Subscriber>> getSubscribers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final subscribersData = prefs.getString(_subscribersKey);

      if (subscribersData == null) return [];

      final List<dynamic> subscribersList = jsonDecode(subscribersData);
      return subscribersList.map((json) => Subscriber.fromJson(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // حفظ اسم المستخدم مشفر
  static Future<void> saveUsername(String username, bool remember) async {
    final prefs = await SharedPreferences.getInstance();

    if (remember) {
      await prefs.setString(_usernameKey, username);
      await prefs.setBool(_rememberUsernameKey, true);
    } else {
      await prefs.remove(_usernameKey);
      await prefs.setBool(_rememberUsernameKey, false);
    }
  }

  // جلب اسم المستخدم المحفوظ
  static Future<String?> getSavedUsername() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remember = prefs.getBool(_rememberUsernameKey) ?? false;

      if (!remember) return null;

      return prefs.getString(_usernameKey);
    } catch (e) {
      return null;
    }
  }

  // التحقق من حفظ اسم المستخدم
  static Future<bool> isUsernameRemembered() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_rememberUsernameKey) ?? false;
  }

  // مسح جميع البيانات
  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_subscribersKey);
    // لا نمسح اسم المستخدم المحفوظ عند تسجيل الخروج
  }

  // مسح اسم المستخدم المحفوظ
  static Future<void> clearSavedUsername() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_usernameKey);
    await prefs.setBool(_rememberUsernameKey, false);
  }

  // تحديث مشترك واحد في القائمة المحفوظة
  static Future<void> updateSubscriber(Subscriber updatedSubscriber) async {
    final subscribers = await getSubscribers();
    final index = subscribers.indexWhere((s) => s.id == updatedSubscriber.id);
    
    if (index != -1) {
      subscribers[index] = updatedSubscriber;
      await saveSubscribers(subscribers);
    }
  }

  // البحث في المشتركين المحفوظين
  static Future<List<Subscriber>> searchSubscribers(String query) async {
    final subscribers = await getSubscribers();
    
    if (query.isEmpty) return subscribers;
    
    return subscribers.where((subscriber) {
      return subscriber.subscriberNumber.toLowerCase().contains(query.toLowerCase()) ||
             subscriber.subscriberName.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }
}
