import 'package:shared_preferences/shared_preferences.dart';
import 'encryption_service.dart';
import '../models/user.dart';

class StorageService {
  static const String _userKey = 'user_data';
  static const String _subscribersKey = 'subscribers_data';
  static const String _usernameKey = 'saved_username';
  static const String _rememberUsernameKey = 'remember_username';

  // حفظ بيانات المستخدم مشفرة
  static Future<void> saveUser(User user  ) async {

    
    final prefs = await SharedPreferences.getInstance();
    final encryptedData = EncryptionService.encryptObject(user.toJson());
    await prefs.setString(_userKey, encryptedData);
  }

  // جلب بيانات المستخدم
  static Future<User?> getUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString(_userKey);
      
      if (encryptedData == null) return null;
      
      final userData = EncryptionService.decryptToMap(encryptedData);
      if (userData == null) return null;
      
      return User.fromJson(userData);
    } catch (e) {
      return null;
    }
  }

  // حفظ قائمة المشتركين مشفرة
  static Future<void> saveSubscribers(List<Subscriber> subscribers) async {
    final prefs = await SharedPreferences.getInstance();
    final subscribersJson = subscribers.map((s) => s.toJson()).toList();
    final encryptedData = EncryptionService.encryptObject(subscribersJson);
    await prefs.setString(_subscribersKey, encryptedData);
  }

  // جلب قائمة المشتركين
  static Future<List<Subscriber>> getSubscribers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString(_subscribersKey);
      
      if (encryptedData == null) return [];
      
      final subscribersData = EncryptionService.decryptToList<Subscriber>(
        encryptedData,
        (json) => Subscriber.fromJson(json),
      );
      
      return subscribersData ?? [];
    } catch (e) {
      return [];
    }
  }

  // حفظ اسم المستخدم مشفر
  static Future<void> saveUsername(String username, bool remember) async {
    final prefs = await SharedPreferences.getInstance();
    
    if (remember) {
      final encryptedUsername = EncryptionService.encrypt(username);
      await prefs.setString(_usernameKey, encryptedUsername);
      await prefs.setBool(_rememberUsernameKey, true);
    } else {
      await prefs.remove(_usernameKey);
      await prefs.setBool(_rememberUsernameKey, false);
    }
  }

  // جلب اسم المستخدم المحفوظ
  static Future<String?> getSavedUsername() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remember = prefs.getBool(_rememberUsernameKey) ?? false;
      
      if (!remember) return null;
      
      final encryptedUsername = prefs.getString(_usernameKey);
      if (encryptedUsername == null) return null;
      
      return EncryptionService.decrypt(encryptedUsername);
    } catch (e) {
      return null;
    }
  }

  // التحقق من حفظ اسم المستخدم
  static Future<bool> isUsernameRemembered() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_rememberUsernameKey) ?? false;
  }

  // مسح جميع البيانات
  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_subscribersKey);
    // لا نمسح اسم المستخدم المحفوظ عند تسجيل الخروج
  }

  // مسح اسم المستخدم المحفوظ
  static Future<void> clearSavedUsername() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_usernameKey);
    await prefs.setBool(_rememberUsernameKey, false);
  }

  // تحديث مشترك واحد في القائمة المحفوظة
  static Future<void> updateSubscriber(Subscriber updatedSubscriber) async {
    final subscribers = await getSubscribers();
    final index = subscribers.indexWhere((s) => s.id == updatedSubscriber.id);
    
    if (index != -1) {
      subscribers[index] = updatedSubscriber;
      await saveSubscribers(subscribers);
    }
  }

  // البحث في المشتركين المحفوظين
  static Future<List<Subscriber>> searchSubscribers(String query) async {
    final subscribers = await getSubscribers();
    
    if (query.isEmpty) return subscribers;
    
    return subscribers.where((subscriber) {
      return subscriber.subscriberNumber.toLowerCase().contains(query.toLowerCase()) ||
             subscriber.subscriberName.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }
}
