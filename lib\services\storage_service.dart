import 'dart:convert';
import 'package:elecollect_app/services/encryption_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'api_service.dart';

class StorageService {
  static const String _userKey = 'user_data';
  static const String _subscribersKey = 'subscribers_data';
  static const String _usernameKey = 'saved_username';
  static const String _rememberUsernameKey = 'remember_username';
  static const String _authTokenKey = 'auth_token';

  // حفظ بيانات المستخدم
  static Future<void> saveUser(User user) async {

    //بدون تشفير
   // final prefs = await SharedPreferences.getInstance();
   // await prefs.setString(_userKey, jsonEncode(user.toJson()));


// مع التشفير
    final prefs = await SharedPreferences.getInstance();
    final encryptedData = EncryptionService.encryptObjectLocal(user.toJson());
    await prefs.setString(_userKey, encryptedData);

  }

  // جلب بيانات المستخدم
  static Future<User?> getUser() async {
    try {


  //بدون تشفير
  //      final prefs = await SharedPreferences.getInstance();
 //     final userData = prefs.getString(_userKey);
          
  //        if (userData == null) return null;
 //    final userMap = jsonDecode(userData) as Map<String, dynamic>;
 //     return User.fromJson(userMap);



// مع التشفير
    final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString(_userKey);

      print("encryptedData" + encryptedData.toString());

      if (encryptedData == null) return null;
      
      final userData = EncryptionService.decryptToMap(encryptedData);
      if (userData == null) return null;
      
      return User.fromJson(userData);


    } catch (e) {
      return null;
    }
  }


   

  // حفظ التوكن
  static Future<void> saveAuthToken(String token) async {


// بدون تشفير
  //final prefs = await SharedPreferences.getInstance();
   // await prefs.setString(_authTokenKey, token);
   // ApiService.setAuthToken(token);

    // مع التشفير
    final prefs = await SharedPreferences.getInstance();
     final encryptedData = EncryptionService.encrypt(token);
    await prefs.setString(_authTokenKey, encryptedData);
    ApiService.setAuthToken(token);
  }

  // جلب التوكن
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();

    //بدون تشفير 
   // final token = prefs.getString(_authTokenKey);
    

// مع التشفير
      final tokenEnc = prefs.getString(_authTokenKey);
      if(tokenEnc == null) return null;
      print(tokenEnc);
      final decryptedData = EncryptionService.decrypt(tokenEnc!);
      final token = decryptedData;

    if (token != null) {
      ApiService.setAuthToken(token);
    }
    return token;
  }

  // حفظ قائمة المشتركين
  static Future<void> saveSubscribers(List<Subscriber> subscribers) async {

    // بدون التشغير
  //  final prefs = await SharedPreferences.getInstance();
   // final subscribersJson = subscribers.map((s) => s.toJson()).toList();
   // await prefs.setString(_subscribersKey, jsonEncode(subscribersJson));


    // مع التشفير 
        final prefs = await SharedPreferences.getInstance();
    final subscribersJson = subscribers.map((s) => s.toJson()).toList();
    final encryptedData = EncryptionService.encryptObjectLocal(subscribersJson);
    await prefs.setString(_subscribersKey, encryptedData);
  }

  // جلب قائمة المشتركين
  static Future<List<Subscriber>> getSubscribers() async {
  
  
  // بدون تشفير
  /*  try {
      final prefs = await SharedPreferences.getInstance();
      final subscribersData = prefs.getString(_subscribersKey);

      if (subscribersData == null) return [];

      final List<dynamic> subscribersList = jsonDecode(subscribersData);
      return subscribersList.map((json) => Subscriber.fromJson(json)).toList();
    } catch (e) {
      return [];
    }*/


// مع التشفير
try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString(_subscribersKey);
      
      if (encryptedData == null) return [];
      
      final subscribersData = EncryptionService.decryptToListLocal<Subscriber>(
        encryptedData,
        (json) => Subscriber.fromJson(json),
      );
      
      return subscribersData ?? [];
    } catch (e) {
      return [];
    }


  }

  // حفظ اسم المستخدم مشفر
  static Future<void> saveUsername(String username, bool remember) async {
    final prefs = await SharedPreferences.getInstance();

    if (remember) {

       final encryptedData = EncryptionService.encrypt(username);

      await prefs.setString(_usernameKey, encryptedData);
      await prefs.setBool(_rememberUsernameKey, true);
    } else {
      await prefs.remove(_usernameKey);
      await prefs.setBool(_rememberUsernameKey, false);
    }
  }

  // جلب اسم المستخدم المحفوظ
  static Future<String?> getSavedUsername() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remember = prefs.getBool(_rememberUsernameKey) ?? false;



      if (!remember) return null;

    //بدون التشفير
     //return prefs.getString(_usernameKey);


// مع التشفير  
       final encryptedData = EncryptionService.decrypt(prefs.getString(_usernameKey)!);
      return encryptedData;
    } catch (e) {
      return null;
    }
  }

  // التحقق من حفظ اسم المستخدم
  static Future<bool> isUsernameRemembered() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_rememberUsernameKey) ?? false;
  }

  // مسح جميع البيانات
  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_subscribersKey);
    // لا نمسح اسم المستخدم المحفوظ عند تسجيل الخروج
  }

  // مسح اسم المستخدم المحفوظ
  static Future<void> clearSavedUsername() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_usernameKey);
    await prefs.setBool(_rememberUsernameKey, false);
  }

  // تحديث مشترك واحد في القائمة المحفوظة
  static Future<void> updateSubscriber(Subscriber updatedSubscriber) async {
    final subscribers = await getSubscribers();
    final index = subscribers.indexWhere((s) => s.id == updatedSubscriber.id);
    
    if (index != -1) {
      subscribers[index] = updatedSubscriber;
      await saveSubscribers(subscribers);
    }
  }

  // البحث في المشتركين المحفوظين
  static Future<List<Subscriber>> searchSubscribers(String query) async {
    final subscribers = await getSubscribers();
    
    if (query.isEmpty) return subscribers;
    
    return subscribers.where((subscriber) {
      return subscriber.subscriberNumber.toLowerCase().contains(query.toLowerCase()) ||
             subscriber.subscriberName.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }
}
