import 'package:flutter/material.dart';
import '../services/storage_service.dart';
import '../services/api_service.dart';
import '../models/user.dart';
import '../theme/app_colors.dart';
import '../theme/app_styles.dart';
import '../theme/app_theme.dart';
import 'collection_screen.dart';
import 'meter_reading_screen.dart';

enum SubscriberActionType {
  view,
  collection,
  meterReading,
}

class SubscribersScreen extends StatefulWidget {
  final SubscriberActionType actionType;

  const SubscribersScreen({
    super.key,
    this.actionType = SubscriberActionType.view,
  });

  @override
  State<SubscribersScreen> createState() => _SubscribersScreenState();
}

class _SubscribersScreenState extends State<SubscribersScreen> {
  final _searchController = TextEditingController();
  List<Subscriber> _allSubscribers = [];
  List<Subscriber> _displayedSubscribers = [];
  bool _isLoading = false;
  bool _isSearching = false;

  // Pagination variables
  static const int _pageSize = 50; // عرض 50 مشترك في كل مرة
  int _currentPage = 0;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  // Search optimization
  String _lastSearchQuery = '';
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadSubscribers();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      _loadMoreSubscribers();
    }
  }

  Future<void> _loadSubscribers() async {
    setState(() {
      _isLoading = true;
      _currentPage = 0;
      _hasMoreData = true;
    });

    try {
      final subscribers = await StorageService.getSubscribers();
      setState(() {
        _allSubscribers = subscribers;
        _displayedSubscribers = _getPagedSubscribers(0);
        _hasMoreData = _allSubscribers.length > _pageSize;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Subscriber> _getPagedSubscribers(int page) {
    final startIndex = page * _pageSize;
    final endIndex = (startIndex + _pageSize).clamp(0, _allSubscribers.length);

    if (startIndex >= _allSubscribers.length) {
      return [];
    }

    return _allSubscribers.sublist(startIndex, endIndex);
  }

  Future<void> _loadMoreSubscribers() async {
    if (_isLoadingMore || !_hasMoreData || _isSearching) return;

    setState(() {
      _isLoadingMore = true;
    });

    await Future.delayed(const Duration(milliseconds: 300)); // تأخير بسيط لتحسين UX

    final nextPage = _currentPage + 1;
    final newSubscribers = _getPagedSubscribers(nextPage);

    if (newSubscribers.isNotEmpty) {
      setState(() {
        _currentPage = nextPage;
        _displayedSubscribers.addAll(newSubscribers);
        _hasMoreData = (_currentPage + 1) * _pageSize < _allSubscribers.length;
      });
    } else {
      setState(() {
        _hasMoreData = false;
      });
    }

    setState(() {
      _isLoadingMore = false;
    });
  }

  Future<void> _syncSubscribers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final subscribers = await ApiService.getSubscribers();

      if (subscribers.isNotEmpty) {
        await StorageService.saveSubscribers(subscribers);
        setState(() {
          _allSubscribers = subscribers;
          _currentPage = 0;
          _displayedSubscribers = _getPagedSubscribers(0);
          _hasMoreData = _allSubscribers.length > _pageSize;
          _isSearching = false;
          _searchController.clear();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث البيانات بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا توجد بيانات جديدة'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المزامنة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _searchSubscribers(String query) {
    setState(() {
      _isSearching = query.isNotEmpty;
      _lastSearchQuery = query;

      if (query.isEmpty) {
        // إعادة تعيين العرض التدريجي
        _currentPage = 0;
        _displayedSubscribers = _getPagedSubscribers(0);
        _hasMoreData = _allSubscribers.length > _pageSize;
      } else {
        // البحث مع تحديد النتائج
        final searchResults = _allSubscribers.where((subscriber) {
          return subscriber.subscriberNumber.toLowerCase().contains(query.toLowerCase()) ||
                 subscriber.subscriberName.toLowerCase().contains(query.toLowerCase());
        }).toList();

        // عرض أول 50 نتيجة من البحث
        _displayedSubscribers = searchResults.take(_pageSize).toList();
        _hasMoreData = false; // إيقاف التحميل التدريجي أثناء البحث
      }
    });
  }

  void _showAllSubscribers() {
    setState(() {
      _searchController.clear();
      _isSearching = false;
      _lastSearchQuery = '';
      _currentPage = 0;
      _displayedSubscribers = _getPagedSubscribers(0);
      _hasMoreData = _allSubscribers.length > _pageSize;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'المشتركون',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.themeData.primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: _isLoading ? null : _syncSubscribers,
            tooltip: 'مزامنة البيانات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.themeData.primaryColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextField(
                      controller: _searchController,
                      onChanged: _searchSubscribers,
                      decoration: const InputDecoration(
                        hintText: 'البحث برقم أو اسم المشترك...',
                        prefixIcon: Icon(Icons.search),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.clear_all),
                    onPressed: _showAllSubscribers,
                    tooltip: 'عرض الكل',
                  ),
                ),
              ],
            ),
          ),

          // مؤشر عدد المشتركين
          if (!_isLoading && _allSubscribers.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _isSearching
                        ? 'نتائج البحث: ${_displayedSubscribers.length}'
                        : 'عرض ${_displayedSubscribers.length} من ${_allSubscribers.length} مشترك',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (!_isSearching && _hasMoreData)
                    const Text(
                      'مرر لأسفل لعرض المزيد',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),

          // قائمة المشتركين
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : _displayedSubscribers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _isSearching ? Icons.search_off : Icons.people_outline,
                              size: 64,
                              color: Colors.grey,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _isSearching ? 'لا توجد نتائج للبحث' : 'لا توجد بيانات مشتركين',
                              style: const TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                              ),
                            ),
                            if (!_isSearching) ...[
                              const SizedBox(height: 16),
                              ElevatedButton.icon(
                                onPressed: _syncSubscribers,
                                icon: const Icon(Icons.sync),
                                label: const Text('مزامنة البيانات'),
                                style: AppStyles.primaryButton,
                              ),
                            ],
                          ],
                        ),
                      )
                    : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _displayedSubscribers.length + (_hasMoreData ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _displayedSubscribers.length) {
                            // Loading indicator for more data
                            return _isLoadingMore
                                ? const Padding(
                                    padding: EdgeInsets.all(16.0),
                                    child: Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  )
                                : const SizedBox.shrink();
                          }

                          final subscriber = _displayedSubscribers[index];
                          return _buildSubscriberCard(subscriber);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriberCard(Subscriber subscriber) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المشترك الأساسية
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        subscriber.subscriberName,
                        style: AppStyles.headingSmall.copyWith(
                          color: AppTheme.themeData.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'رقم المشترك: ${subscriber.subscriberNumber}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: subscriber.currentBalance > 0
                        ? Colors.red.withAlpha(1)
                        : Colors.green.withAlpha(1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${subscriber.currentBalance.toStringAsFixed(2)} ر.ي',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: subscriber.currentBalance > 0 ? Colors.red : Colors.green,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // أزرار العمليات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => Directionality(
                            textDirection: TextDirection.rtl,
                            child: CollectionScreen(
                              subscriber: subscriber,
                            ),
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.payment, size: 18),
                    label: const Text('تحصيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => Directionality(
                            textDirection: TextDirection.rtl,
                            child: MeterReadingScreen(
                              prefilledMeterNumber: subscriber.meterNumber,
                              subscriberInfo: subscriber,
                            ),
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.qr_code_scanner, size: 18),
                    label: const Text('قراءة العداد'),
                    style: AppStyles.primaryButton,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

}
