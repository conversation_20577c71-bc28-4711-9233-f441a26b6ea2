import 'package:elecollect_app/models/collection.dart';
import 'package:elecollect_app/models/invoice_receipt.dart';
import 'package:elecollect_app/screens/thermal_printer_screen.dart';
import 'package:elecollect_app/services/bluetooth_thermal_printer.dart';
import 'package:elecollect_app/services/storage_service.dart';
import 'package:flutter/material.dart';
import 'package:data_table_2/data_table_2.dart';
import '../models/user.dart';
import '../models/collection_report.dart';
import '../services/api_service.dart';
import '../services/excel_export_service.dart';
import '../theme/app_theme.dart';




class CollectionReportsScreen extends StatefulWidget {
  const CollectionReportsScreen({super.key});

  @override
  State<CollectionReportsScreen> createState() => _CollectionReportsScreenState();
}

class _CollectionReportsScreenState extends State<CollectionReportsScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  String? _selectedSubscriber;
  String? _selectedSubscriberName;
  final TextEditingController _subscriberSearchController = TextEditingController();
  final TextEditingController _subscriberNoController = TextEditingController();
  DateTime? _fromDate;
  DateTime? _toDate;

  // Data
  List<Subscriber> _allSubscribers = [];
  List<Subscriber> _filteredSubscribers = [];
  List<BillingsRec> _collections = [];
  CollectionReportSummary? _summary;

  // State
  bool _isLoading = false;
  bool _isLoadingSubscribers = false;
  bool _hasSearched = false;
  bool _showSubscriberDropdown = false;
  User? _currentUser;
  @override
  void initState() {
    super.initState();
    _loadSubscribers();

     _fromDate = DateTime.now().subtract(const Duration(days: 30));
      _toDate = DateTime.now();
  }

  Future<void> _loadSubscribers() async {
    final user = await StorageService.getUser();
    setState(() {
      _isLoadingSubscribers = true;
          _currentUser = user;
    });

    try {
      // تحميل المشتركين بشكل تدريجي لتحسين الأداء
      final subscribers = await StorageService.getSubscribers() ;//await ApiService.getSubscribers(); 

      // إذا كان العدد كبير، نحمل فقط أول 100 مشترك في البداية
      if (subscribers.length > 100) {
        setState(() {
          _allSubscribers = subscribers;
          _filteredSubscribers = subscribers.take(50).toList(); // أول 50 فقط للعرض
        });
      } else {
        setState(() {
          _allSubscribers = subscribers;
          _filteredSubscribers = subscribers;
        });
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في جلب المشتركين: ${e.toString()}');
    } finally {
      setState(() {
        _isLoadingSubscribers = false;
      });
    }
  }

  Future<void> _generateReport() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _hasSearched = false;
    });

    try {

          final request = CollectionReportRequest(
userid: null , // _currentUser!.id  , 
Billing_No:  _selectedSubscriber ?? "",
            Date_From:   '${_fromDate!.year}/${_fromDate!.month.toString().padLeft(2, '0')}/${_fromDate!.day.toString().padLeft(2, '0')}'   ,
            Date_To:  '${_toDate!.year}/${_toDate!.month.toString().padLeft(2, '0')}/${_toDate!.day.toString().padLeft(2, '0')}'   ,

       
      );

      final response = await ApiService.getCollectionReports( request , context
       // subscriberNumber: _selectedSubscriber,
 
      );

      if (response.success) {
        print(response.collections.length);
        setState(() {
          _collections = response.collections;
          _summary = response.summary;
          _hasSearched = true;
        });
      } else {
        _showErrorSnackBar(response.message);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في جلب التقرير: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }





  Future<void> _exportToExcel() async {
    if (_collections.isEmpty) {
      _showErrorSnackBar('لا توجد بيانات للتصدير');
      return;
    }

    try {
      final filePath = await ExcelExportService.exportCollectionReport(
        collections: _collections,
        summary: _summary,
        subscriberFilter: _selectedSubscriber,
        fromDate: _fromDate,
        toDate: _toDate,
      );

      if (filePath != null) {
        _showSuccessSnackBar('تم تصدير التقرير بنجاح');
      } else {
        _showErrorSnackBar('خطأ في تصدير التقرير');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في التصدير: ${e.toString()}');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير التحصيلات'),
        backgroundColor: AppTheme.themeData.primaryColor ,//Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
          // Filter Form
          Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Text(
                      'فلترة التقرير',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Subscriber selection with search
                    _buildSubscriberSearchField(),

                    const SizedBox(height: 8),

                    // Quick number search
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _subscriberNoController,
                            decoration: const InputDecoration(
                              labelText: 'البحث السريع برقم الاشتراك',
                              hintText: 'ادخل رقم المشترك',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.numbers),
                            ),
                            onFieldSubmitted: (value) {
                              if (value.isNotEmpty) {
                                _quickSearchByNumber(value);
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                      if(_subscriberNoController.text.isNotEmpty) {
                            _quickSearchByNumber(_subscriberNoController.text);
                      } else{
                                _selectedSubscriber = null;
                              _selectedSubscriberName = null;
                             _subscriberSearchController.clear();
                      }
                      
                       
                       
                          
                            });
                          },
                          icon: const Icon(Icons.search),
                          label: const Text('بحث برقم المشترك'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Date range
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _fromDate ?? DateTime.now().subtract(const Duration(days: 30)),
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now(),
                              );
                              if (date != null) {
                                setState(() {
                                  _fromDate = date;
                                });
                              }
                            },
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'من تاريخ',
                                border: OutlineInputBorder(),
                              ),
                              child: Text(
                                _fromDate != null 
                                    ? '${_fromDate!.year}/${_fromDate!.month.toString().padLeft(2, '0')}/${_fromDate!.day.toString().padLeft(2, '0')}'
                                    : 'اختر التاريخ',
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: InkWell(
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: _toDate ?? DateTime.now(),
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now(),
                              );
                              if (date != null) {
                                setState(() {
                                  _toDate = date;
                                });
                              }
                            },
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'إلى تاريخ',
                                border: OutlineInputBorder(),
                              ),
                              child: Text(
                                _toDate != null 
                                    ? '${_toDate!.year}/${_toDate!.month.toString().padLeft(2, '0')}/${_toDate!.day.toString().padLeft(2, '0')}'
                                    : 'اختر التاريخ',
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _generateReport,
                            icon: _isLoading 
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(Icons.search),
                            label: Text(_isLoading ? 'جاري البحث...' : 'عرض التقرير'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _collections.isEmpty ? null : _exportToExcel,
                            icon: const Icon(Icons.file_download),
                            label: const Text('تصدير CSV'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Results
          if (_hasSearched) ...[
            // Summary
            if (_summary != null) _buildSummaryCard(),

            // Data table
            _buildDataTable(),
          ] else if (!_isLoading)
            const SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'اختر المعايير واضغط "عرض التقرير"',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص التقرير',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المعاملات',
                    '${_summary!.totalTransactions}',
                    Icons.receipt,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المبلغ المدفوع',
                    '${_summary!.totalAmountPaid.toStringAsFixed(2)} ريال',
                    Icons.payments,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          /*  Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'المبلغ المستحق',
                    '${_summary!.totalAmountDue.toStringAsFixed(2)} ريال',
                    Icons.account_balance_wallet,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المتبقي',
                    '${_summary!.totalOutstanding.toStringAsFixed(2)} ريال',
                    Icons.warning,
                    Colors.red,
                  ),
                ),
              ],
            ),*/
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: color.withAlpha(1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDataTable() {
    if (_collections.isEmpty) {
      return const SizedBox(
        height: 200,
        child: Center(
          child: Text(
            'لا توجد بيانات للعرض',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      );
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: SizedBox(
          height: 400, // ارتفاع محدد للجدول
          child: DataTable2(
            columnSpacing: 12,
            horizontalMargin: 12,
            minWidth: 1200,
            columns: const [
            DataColumn2(
              label: Text('رقم المستند', style: TextStyle(fontWeight: FontWeight.bold)),
              size: ColumnSize.S,
            ),
            DataColumn2(
              label: Text('المشترك', style: TextStyle(fontWeight: FontWeight.bold)),
              size: ColumnSize.L,
            ),
            DataColumn2(
              label: Text('المبلغ المدفوع', style: TextStyle(fontWeight: FontWeight.bold)),
              size: ColumnSize.S,
            ),
            DataColumn2(
              label: Text('طريقة الدفع', style: TextStyle(fontWeight: FontWeight.bold)),
              size: ColumnSize.S,
            ),
            DataColumn2(
              label: Text('التاريخ', style: TextStyle(fontWeight: FontWeight.bold)),
              size: ColumnSize.S,
            ),
            DataColumn2(
              label: Text('المحصل', style: TextStyle(fontWeight: FontWeight.bold)),
              size: ColumnSize.S,
            ),
                  DataColumn2(
              label: Text('طباعة', style: TextStyle(fontWeight: FontWeight.bold)),
              size: ColumnSize.S,
            ),
          ],
          rows: _collections.map((collection) => DataRow2(
            cells: [
              DataCell(Text(collection.id.toString())),
              DataCell(
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      collection.partnerId![1],
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                     collection.partnerId![0].toString(),
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),
              DataCell(
                Text(
                  '${collection.amountTotal!.toStringAsFixed(2)} ريال',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
              DataCell(Text( "نقد")),
             
              DataCell(
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(collection.dateInvoice!.toString()),
                    Text(
                      collection.dateInvoice!.toString(),
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),
              DataCell(Text(collection.userId![1])),  
 


 DataCell(
    ElevatedButton.icon(
                    onPressed: () {
                //  _buildCollectionButton(collection);
                   showSuccessDialog(collection);
                    },
                    icon: const Icon(Icons.print_sharp, size: 18),
                    label: const Text('طباعة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
 )
            ],
          )).toList(),
          ),
        ),
      ),
    );
  }


 /* Widget _buildCollectionButton(BillingsRec vData) {
    return  
 
      ElevatedButton(
        onPressed:   showSuccessDialog(vData) ,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
        ),
        child: 
            const Text(
              'طباعة',
              style: TextStyle(fontSize: 16),
            ),
      );
     
  }*/



    showSuccessDialog(BillingsRec vData) {

     var  _lastCollection = CollectionResponse (

    success : true , 
   message : "",
  docId : vData.id.toString(),
    newBalance : 0 ,
  transactionDate : vData.dateInvoice

      );

  var _lastSuccessfulInvoiceReceipt = InvoiceReceipt(
              docId: vData.displayName ,// vData.id,
              transactionId: null,
              displayName: vData.displayName, // يجب ان يتم ارجاعه من ال api
              subscriberNumber: vData.partnerId[0].toString(),
              subscriberName: vData.partnerId[1].toString(),
              meterNumber:null,
              dateInvoice: vData.dateInvoice!,
              datePrint: DateTime.now(),
              dueAmount: vData.xAmountTotal ?? 0,
              paidAmount: vData.amountTotal!,
              newBalance: 0,
              currency: "ريال",
              paymentType:vData.paymentType!,
              collector: vData.userId[1].toString(),
              userPrint: _currentUser!.fullName,
              notes: null
              );

         _printThermalReceipt(
          _lastSuccessfulInvoiceReceipt
          // _lastCollection, vData.partnerId![1] ,  '${vData.amountTotal!.toStringAsFixed(2)} ريال' ,   'Cash' , '' 
           );

   /* showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('طباعة المستند'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('رقم المستند: ${vData.id}'),
            const SizedBox(height: 8),
           // Text('الرصيد الجديد: ${response.newBalance?.toStringAsFixed(2)} ريال'),
            //const SizedBox(height: 8),
            Text('تاريخ المستند: ${vData.dateInvoice?.toString().split(' ')[0]}'),
          ],
        ),
        actions: [
          // Print Receipt Button
          /*ElevatedButton.icon(
            onPressed: () async {
              await _printReceipt();
            },
            icon: const Icon(Icons.print),
            label: const Text('طباعة السند'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),*/

             PopupMenuButton<String>(
            onSelected: (value) async {
              if (value == 'thermal') {
                await _printThermalReceipt( _lastCollection, vData.partnerId![1] ,  '${vData.amountTotal!.toStringAsFixed(2)} ريال' ,   'Cash' , '' );
              /*} else if (value == 'pdf') {
                await _printPdfReceipt();*/
              } else if (value == 'settings') {
                _openThermalPrinterSettings( );
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'thermal',
                child: Row(
                  children: [
                    Icon(Icons.bluetooth, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('طباعة حرارية مباشرة'),
                  ],
                ),
              ),
              /*const PopupMenuItem(
                value: 'pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf, color: Colors.red),
                    SizedBox(width: 8),
                    Text('طباعة PDF'),
                  ],
                ),
              ),*/
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, color: Colors.grey),
                    SizedBox(width: 8),
                    Text('إعدادات الطابعة الحرارية'),
                  ],
                ),
              ),
            ],
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.print, color: Colors.white),
                  SizedBox(width: 8),
                  Text('طباعة السند', style: TextStyle(color: Colors.white)),
                  SizedBox(width: 4),
                  Icon(Icons.arrow_drop_down, color: Colors.white),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(); // العودة للشاشة السابقة
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );*/

    
  }

  void _openThermalPrinterSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ThermalPrinterScreen(),
      ),
    );
  }
  // Print directly to thermal printer
  Future<void> _printThermalReceipt(
    _lastSuccessfulInvoiceReceipt
   // _lastSuccessfulCollection ,
   //  _subscriber ,
   //    _lastAmountPaid ,
   //     _lastPaymentMethod   ,
   //      _lastNotes 
         
          ) async {
    if (_lastSuccessfulInvoiceReceipt == null 
    //||
      //  _lastAmountPaid == null ||
      //  _lastPaymentMethod == null
        ) {
      _showErrorSnackBar('لا توجد بيانات تحصيل للطباعة');
      return;
    }

    try {
      // Check if connected to thermal printer
      if (!BluetoothThermalPrinter.isConnected()) {
        _showErrorSnackBar('الطابعة الحرارية غير متصلة. يرجى الاتصال بالطابعة من الإعدادات.');
        return;
      }

      await BluetoothThermalPrinter.printCollectionReceipt(
        invoiceReceipt: _lastSuccessfulInvoiceReceipt,
       // subscriber: _subscriber,
       // collectionResponse: _lastSuccessfulCollection!,
       // amountPaid: _lastAmountPaid!,
       // paymentMethod: _lastPaymentMethod!,
       // notes: _lastNotes,
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم طباعة السند على الطابعة الحرارية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        print('${e.toString()}');
        _showErrorSnackBar('خطأ في الطباعة الحرارية: ${e.toString()}');
      }
    }
  }


  Widget _buildSubscriberSearchField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search field
        TextFormField(
          controller: _subscriberSearchController,
          decoration: InputDecoration(
            labelText: 'البحث عن مشترك',
            hintText: 'ادخل رقم الاشتراك أو اسم المشترك',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _subscriberSearchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _subscriberSearchController.clear();
                      setState(() {
                        _selectedSubscriber = null;
                        _selectedSubscriberName = null;
                        _filteredSubscribers = _allSubscribers;
                        _showSubscriberDropdown = false;
                      });
                    },
                  )
                : null,
          ),
          onChanged: _onSubscriberSearchChanged,
          onTap: () {
            setState(() {
              // إظهار القائمة عند النقر، مع عرض بعض المشتركين إذا كان الحقل فارغ
              if (_subscriberSearchController.text.isEmpty) {
                _filteredSubscribers = _allSubscribers.take(20).toList(); // أول 20 مشترك
              }
              _showSubscriberDropdown = _filteredSubscribers.isNotEmpty;
            });
          },
        ),

        // Selected subscriber display
        if (_selectedSubscriber != null) ...[
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withAlpha(3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.person, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '$_selectedSubscriber - $_selectedSubscriberName',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: () {
                    setState(() {
                      _selectedSubscriber = null;
                      _selectedSubscriberName = null;
                      _subscriberSearchController.clear();
                     _subscriberNoController.clear();
                      _showSubscriberDropdown = false;
                    });
                  },
                ),
              ],
            ),
          ),
        ],

        // Dropdown with filtered results
        if (_showSubscriberDropdown) ...[
          const SizedBox(height: 4),
          Container(
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withAlpha(3)),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: _filteredSubscribers.isNotEmpty
                ? ListView.builder(
                    shrinkWrap: true,
                    itemCount: _filteredSubscribers.length.clamp(0, 50), // حد أقصى 50 نتيجة
                    itemBuilder: (context, index) {
                      final subscriber = _filteredSubscribers[index];
                      return ListTile(
                        dense: true,
                        leading: const Icon(Icons.person, size: 20),
                        title: Text(
                          subscriber.subscriberName,
                          style: const TextStyle(fontSize: 14),
                        ),
                        subtitle: Text(
                          subscriber.subscriberNumber,
                          style: const TextStyle(fontSize: 12),
                        ),
                        onTap: () {
                          setState(() {
                            _selectedSubscriber = subscriber.subscriberNumber;
                            _selectedSubscriberName = subscriber.subscriberName;
                            _subscriberSearchController.text = '${subscriber.subscriberNumber} - ${subscriber.subscriberName}';
                            _showSubscriberDropdown = false;
                          });
                        },
                      );
                    },
                  )
                : const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'لا توجد نتائج مطابقة',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                  ),
          ),
        ],

        // "All subscribers" option
        if (_showSubscriberDropdown) ...[
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.withAlpha(3)),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.withAlpha(1),
            ),
            child: ListTile(
              dense: true,
              leading: const Icon(Icons.group, size: 20),
              title: const Text(
                'جميع المشتركين',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              onTap: () {
                setState(() {
                  _selectedSubscriber = null;
                  _selectedSubscriberName = null;
                  _subscriberSearchController.text = 'جميع المشتركين';
                  _showSubscriberDropdown = false;
                });
              },
            ),
          ),
        ],
      ],
    );
  }

  void _onSubscriberSearchChanged(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredSubscribers = _allSubscribers.take(50).toList(); // حد أقصى 50 للأداء
        _showSubscriberDropdown = false; // إخفاء القائمة عند عدم وجود بحث
      } else {
        // البحث في المشتركين
        _filteredSubscribers = _allSubscribers.where((subscriber) {
          final searchLower = query.toLowerCase();
          final matchesNumber = subscriber.subscriberNumber.toLowerCase().contains(searchLower);
          final matchesName = subscriber.subscriberName.toLowerCase().contains(searchLower);
          return matchesNumber || matchesName;
        }).take(50).toList(); // حد أقصى 50 نتيجة

        // إظهار القائمة فقط إذا كان هناك نتائج
        _showSubscriberDropdown = _filteredSubscribers.isNotEmpty;
      }
    });
  }

  void _quickSearchByNumber(String subscriberNumber) {
    final subscriber = _allSubscribers.firstWhere(
      (s) => s.subscriberNumber.toLowerCase() == subscriberNumber.toLowerCase(),
      orElse: () => _allSubscribers.firstWhere(
        (s) => s.subscriberNumber.toLowerCase().contains(subscriberNumber.toLowerCase()),
        orElse: () => Subscriber(
          id: 0,
          subscriberNumber: '',
          subscriberName: '',
          currentBalance: 0,
          lastReading: 0,
        ),
      ),
    );

    if (subscriber.subscriberNumber.isNotEmpty) {
      setState(() {
        _selectedSubscriber = subscriber.subscriberNumber;
        _selectedSubscriberName = subscriber.subscriberName;
        _subscriberSearchController.text = '${subscriber.subscriberNumber} - ${subscriber.subscriberName}';
        _showSubscriberDropdown = false;
      });
      _showSuccessSnackBar('تم العثور على المشترك: ${subscriber.subscriberName}');
    } else {
      _showErrorSnackBar('لم يتم العثور على مشترك برقم: $subscriberNumber');
    }
  }

  @override
  void dispose() {
    _subscriberSearchController.dispose();
    _subscriberNoController.dispose();
    super.dispose();
  }
}
