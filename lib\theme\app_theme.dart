import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_styles.dart';
import 'app_icons.dart';

/// نظام Theme الشامل للتطبيق
/// المكان الوحيد لتغيير جميع الألوان والأنماط في التطبيق
class AppTheme {
  // ========== إعدادات Theme الحالية ==========
  
  /// نوع النمط المستخدم حالياً
  /// غير هذا لتغيير النمط الكامل للتطبيق
  static ThemeType currentTheme = ThemeType.blue;
  
  /// الحصول على ThemeData للتطبيق
  static ThemeData get themeData {
    switch (currentTheme) {
      case ThemeType.blue:
        return _blueTheme;
      case ThemeType.red:
        return _redTheme;
      case ThemeType.green:
        return _greenTheme;
      case ThemeType.orange:
        return _orangeTheme;
      case ThemeType.purple:
        return _purpleTheme;
      default:
        return _blueTheme;
    }
  }

  // ========== الأنماط المختلفة ==========
  
  /// النمط الأزرق (الافتراضي)
  static ThemeData get _blueTheme {
    return _buildTheme(
      primaryColor: const Color(0xFF1E3A8A),
      secondaryColor: const Color(0xFF059669),
    );
  }
  
  /// النمط الأحمر
  static ThemeData get _redTheme {
    return _buildTheme(
      primaryColor: const Color(0xFFDC2626),
      secondaryColor: const Color(0xFF059669),
    );
  }
  
  /// النمط الأخضر
  static ThemeData get _greenTheme {
    return _buildTheme(
      primaryColor: const Color(0xFF059669),
      secondaryColor: const Color(0xFF3B82F6),
    );
  }
  
  /// النمط البرتقالي
  static ThemeData get _orangeTheme {
    return _buildTheme(
      primaryColor: const Color(0xFFEA580C),
      secondaryColor: const Color(0xFF059669),
    );
  }
  
  /// النمط البنفسجي
  static ThemeData get _purpleTheme {
    return _buildTheme(
      primaryColor: const Color(0xFF7C3AED),
      secondaryColor: const Color(0xFF059669),
    );
  }

  // ========== بناء Theme ==========
  
  /// بناء ThemeData مخصص
  static ThemeData _buildTheme({
    required Color primaryColor,
    required Color secondaryColor,
  }) {
    return ThemeData(
      // الألوان الأساسية
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        secondary: secondaryColor,
        brightness: Brightness.light,
      ),
      
      // خلفية التطبيق
      scaffoldBackgroundColor: AppColors.background,
      
      // نمط AppBar
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: AppColors.textOnDark,
        elevation: 4,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.textOnDark,
        ),
      ),
      
      // نمط الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: AppColors.textOnDark,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 2,
        ),
      ),
      
      // نمط الأزرار المحددة
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: BorderSide(color: primaryColor, width: 1),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // نمط الأزرار النصية
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // نمط البطاقات
     /* cardTheme: const CardThemeData(
        color: AppColors.cardBackground,
        elevation: 4,
        margin: EdgeInsets.all(8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),*/
      
      // نمط حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.borderError, width: 2),
        ),
        filled: true,
        fillColor: AppColors.cardBackground,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      
      // نمط النصوص
      textTheme: const TextTheme(
        headlineLarge: AppStyles.headingLarge,
        headlineMedium: AppStyles.headingMedium,
        headlineSmall: AppStyles.headingSmall,
        bodyLarge: AppStyles.bodyLarge,
        bodyMedium: AppStyles.bodyMedium,
        bodySmall: AppStyles.bodySmall,
      ),
      
      // نمط الأيقونات
      iconTheme: IconThemeData(
        color: primaryColor,
        size: 24,
      ),
      
      // نمط FloatingActionButton
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: AppColors.textOnDark,
      ),
      
      // نمط BottomNavigationBar
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColors.cardBackground,
        selectedItemColor: primaryColor,
        unselectedItemColor: AppColors.textSecondary,
        type: BottomNavigationBarType.fixed,
      ),
      
      // نمط Drawer
      drawerTheme: const DrawerThemeData(
        backgroundColor: AppColors.cardBackground,
      ),
      
      // نمط Dialog
     /* dialogTheme: const DialogThemeData(
        backgroundColor: AppColors.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),*/
      
      // نمط SnackBar
      snackBarTheme: SnackBarThemeData(
        backgroundColor: primaryColor,
        contentTextStyle: const TextStyle(color: AppColors.textOnDark),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
      ),
    );
  }

  // ========== دوال مساعدة ==========
  
  /// إنشاء MaterialColor من Color
  static MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.value, swatch);
  }
  
  /// تغيير نمط التطبيق
  static void changeTheme(ThemeType newTheme) {
    currentTheme = newTheme;
  }
  
  /// الحصول على اللون الأساسي الحالي
  static Color get currentPrimaryColor {
    switch (currentTheme) {
      case ThemeType.blue:
        return const Color(0xFF1E3A8A);
      case ThemeType.red:
        return const Color(0xFFDC2626);
      case ThemeType.green:
        return const Color(0xFF059669);
      case ThemeType.orange:
        return const Color(0xFFEA580C);
      case ThemeType.purple:
        return const Color(0xFF7C3AED);
      default:
        return const Color(0xFF1E3A8A);
    }
  }
  
  /// الحصول على اللون الثانوي الحالي
  static Color get currentSecondaryColor {
    switch (currentTheme) {
      case ThemeType.blue:
        return const Color(0xFF059669);
      case ThemeType.red:
        return const Color(0xFF059669);
      case ThemeType.green:
        return const Color(0xFF3B82F6);
      case ThemeType.orange:
        return const Color(0xFF059669);
      case ThemeType.purple:
        return const Color(0xFF059669);
      default:
        return const Color(0xFF059669);
    }
  }
}

/// أنواع الأنماط المتاحة
enum ThemeType {
  blue,    // الأزرق (الافتراضي)
  red,     // الأحمر
  green,   // الأخضر
  orange,  // البرتقالي
  purple,  // البنفسجي
}

/// أسماء الأنماط بالعربية
extension ThemeTypeExtension on ThemeType {
  String get arabicName {
    switch (this) {
      case ThemeType.blue:
        return 'الأزرق';
      case ThemeType.red:
        return 'الأحمر';
      case ThemeType.green:
        return 'الأخضر';
      case ThemeType.orange:
        return 'البرتقالي';
      case ThemeType.purple:
        return 'البنفسجي';
    }
  }
}

/// إدارة الصور والأصول
class AppAssets {
  // ========== مسارات الصور ==========

  /// شعار التطبيق
  static const String logo = 'assets/images/logo.png';

  /// أيقونة التطبيق
  static const String appIcon = 'assets/images/app_icon.png';

  /// صورة الخلفية
  static const String background = 'assets/images/background.png';

  /// صورة افتراضية للمشترك
  static const String defaultAvatar = 'assets/images/default_avatar.png';

  /// صورة افتراضية للعداد
  static const String defaultMeter = 'assets/images/default_meter.png';

  // ========== أيقونات مخصصة ==========

  /// أيقونة التحصيلات
  static const String collectionsIcon = 'assets/icons/collections.png';

  /// أيقونة قراءة العدادات
  static const String meterReadingIcon = 'assets/icons/meter_reading.png';

  /// أيقونة التقارير
  static const String reportsIcon = 'assets/icons/reports.png';

  /// أيقونة المشتركين
  static const String subscribersIcon = 'assets/icons/subscribers.png';

  // ========== دوال مساعدة ==========

  /// الحصول على صورة بحجم محدد
  static Widget getImage(String path, {double? width, double? height, BoxFit? fit}) {
    return Image.asset(
      path,
      width: width,
      height: height,
      fit: fit ?? BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: width ?? 50,
          height: height ?? 50,
          color: AppColors.border,
          child: const Icon(Icons.image_not_supported),
        );
      },
    );
  }

  /// الحصول على أيقونة مخصصة
  static Widget getCustomIcon(String path, {double size = 24, Color? color}) {
    return Image.asset(
      path,
      width: size,
      height: size,
      color: color,
      errorBuilder: (context, error, stackTrace) {
        return Icon(Icons.image_not_supported, size: size, color: color);
      },
    );
  }
}
