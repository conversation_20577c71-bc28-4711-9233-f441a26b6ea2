import 'package:elecollect_app/models/invoice_receipt.dart';
import 'package:elecollect_app/screens/thermal_printer_screen.dart';
import 'package:elecollect_app/services/bluetooth_thermal_printer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/user.dart';
import '../models/collection.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../services/print_service.dart';
import '../theme/app_theme.dart';

class CollectionScreen extends StatefulWidget {
  final Subscriber subscriber;

  const CollectionScreen({
    super.key,
    required this.subscriber,
  });

  @override
  State<CollectionScreen> createState() => _CollectionScreenState();
}

class _CollectionScreenState extends State<CollectionScreen> {
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _isInquiring = false;
  String _paymentMethod = 'Cash';
  BalanceInquiryResponse? _balanceInfo;
  User? _currentUser;

  // For storing successful collection data for printing
  CollectionResponse? _lastSuccessfulCollection;
  InvoiceReceipt? _lastSuccessfulInvoiceReceipt;

  double? _lastAmountPaid;
  String? _lastPaymentMethod;
  String? _lastNotes;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
    _inquireBalance();
  }

  Future<void> _loadCurrentUser() async {
    final user = await StorageService.getUser();
    setState(() {
      _currentUser = user;
    });
  }

  var transactionId;

  Future<void> _inquireBalance() async {
    // if (_currentUser == null) return;
    print("load");
    transactionId = DateTime.now().millisecondsSinceEpoch.toString();
    setState(() {
      _isInquiring = true;
    });

    try {
      final request = BalanceInquiryRequest(
        subscriberNumber:  widget.subscriber.subscriberNumber,
      );

      final response = await ApiService.inquireBalance(request, context);

      setState(() {
        _balanceInfo = response;
        _isInquiring = false;
      });

      if (!response.success) {
        _showErrorSnackBar(response.message);
      }
    } catch (e) {
      setState(() {
        _isInquiring = false;
      });
      _showErrorSnackBar('خطأ في الاتصال: ${e.toString()}');
    }
  }

  Future<void> _processCollection() async {
    final amount = double.tryParse(_amountController.text);

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد عملية التحصيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // const Text('سيتم ارسال عملية التحصيل '),
            Text('اسم المشترك : ' + widget.subscriber.subscriberName),
            SizedBox(
              height: 20,
            ),
            Text(
              'المبلغ  : ' + amount.toString(),
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد التحصيل'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      if (!_formKey.currentState!.validate() || _currentUser == null) return;

      if (amount == null || amount < 500  || amount > 100000000) {
        _showErrorSnackBar('يرجى إدخال مبلغ صحيح   , اقل مبلغ 500 ريال و اكبر مبلغ 100000000 ريال ');
        return;
      }

      if (_balanceInfo != null) {
        if (amount < _balanceInfo!.Lower && _balanceInfo!.AllowPart == 'N') {
          _showErrorSnackBar(
              'يرجى إدخال مبلغ صحيح   , يجب دفع مبلغ الفاتورة كاملا ');
          return;
        }
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // إنشاء معرف المعاملة

        // تنسيق التاريخ
        final processDate = DateTime.now()
            .toIso8601String()
            .replaceAll(RegExp(r'[^\d]'), '')
            .substring(0, 14);

        final request = CollectionRequest(
          subscriberNumber: widget.subscriber.subscriberNumber,
          dueAmount: _balanceInfo?.currentBalance ?? amount,
          paidAmount: amount,
          paymentType: _paymentMethod.toLowerCase(),
          processDate: processDate,
          currency: 'ry',
          transactionId: transactionId,
        );
        print(transactionId);

        final response = await ApiService.processCollection(request, context);

        setState(() {
          _isLoading = false;
        });

        if (response.success) {
          // Store successful collection data for printing
          _lastSuccessfulCollection = response;
          _lastAmountPaid = amount;
          _lastPaymentMethod = _paymentMethod;
          _lastNotes = _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim();

          _lastSuccessfulInvoiceReceipt = InvoiceReceipt(
              docId: _lastSuccessfulCollection!.docId,
              transactionId: transactionId,
              displayName: '', // يجب ان يتم ارجاعه من ال api
              subscriberNumber: widget.subscriber.subscriberNumber,
              subscriberName: widget.subscriber.subscriberName + '[' + widget.subscriber.meterNumber! + ']' ,
              meterNumber: widget.subscriber.meterNumber,
              dateInvoice: DateTime.now(),
              datePrint: DateTime.now(),
              dueAmount: _balanceInfo?.currentBalance ?? 0,
              paidAmount: amount,
              newBalance: 0,
              currency: "ريال",
              paymentType:_paymentMethod,
              collector: _currentUser!.fullName,
              userPrint: _currentUser!.fullName,
              notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim() );
          _showSuccessDialog(_lastSuccessfulInvoiceReceipt);
        } else {
          _showErrorSnackBar(response.message);
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('خطأ في الاتصال: ${e.toString()}');
      }
    }
  }

  void _showSuccessDialog(InvoiceReceipt? invoiceReceipt) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('تم التحصيل بنجاح'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('رقم السند: ${invoiceReceipt!.docId}'),
            const SizedBox(height: 8),
            // Text('الرصيد الجديد: ${response.newBalance?.toStringAsFixed(2)} ريال'),
            //const SizedBox(height: 8),
            Text(
                'تاريخ السند: ${invoiceReceipt.dateInvoice ?.toString().split(' ')[0]}'),
          ],
        ),
        actions: [
          // Print Receipt Button
          /*ElevatedButton.icon(
            onPressed: () async {
              await _printReceipt();
            },
            icon: const Icon(Icons.print),
            label: const Text('طباعة السند'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),*/

          PopupMenuButton<String>(
            onSelected: (value) async {
              if (value == 'thermal') {
                await _printThermalReceipt();
                /*} else if (value == 'pdf') {
                await _printPdfReceipt();*/
              } else if (value == 'settings') {
                _openThermalPrinterSettings();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'thermal',
                child: Row(
                  children: [
                    Icon(Icons.bluetooth, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('طباعة حرارية مباشرة'),
                  ],
                ),
              ),
              /*const PopupMenuItem(
                value: 'pdf',
                child: Row(
                  children: [
                    Icon(Icons.picture_as_pdf, color: Colors.red),
                    SizedBox(width: 8),
                    Text('طباعة PDF'),
                  ],
                ),
              ),*/
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, color: Colors.grey),
                    SizedBox(width: 8),
                    Text('إعدادات الطابعة الحرارية'),
                  ],
                ),
              ),
            ],
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.print, color: Colors.white),
                  SizedBox(width: 8),
                  Text('طباعة السند', style: TextStyle(color: Colors.white)),
                  SizedBox(width: 4),
                  Icon(Icons.arrow_drop_down, color: Colors.white),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق الحوار
              Navigator.of(context).pop(); // العودة للشاشة السابقة
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // Open thermal printer settings
  void _openThermalPrinterSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ThermalPrinterScreen(),
      ),
    );
  }

  // Print as PDF (original method)
  Future<void> _printPdfReceipt() async {
    if (_lastSuccessfulCollection == null ||
        _lastAmountPaid == null ||
        _lastPaymentMethod == null) {
      _showErrorSnackBar('لا توجد بيانات تحصيل للطباعة');
      return;
    }

    try {
      await PrintService.printCollectionReceipt(
        invoiceReceipt: _lastSuccessfulInvoiceReceipt!,
        //subscriber: widget.subscriber,
        //collectionResponse: _lastSuccessfulCollection!,
        //amountPaid: _lastAmountPaid!,
        //paymentMethod: _lastPaymentMethod!,
        //notes: _lastNotes,
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال السند للطباعة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في طباعة PDF: ${e.toString()}');
      }
    }
  }

  // Print directly to thermal printer
  Future<void> _printThermalReceipt() async {
    if (_lastSuccessfulInvoiceReceipt == null ||
        _lastAmountPaid == null ||
        _lastPaymentMethod == null) {
      _showErrorSnackBar('لا توجد بيانات تحصيل للطباعة');
      return;
    }

    try {
      // Check if connected to thermal printer
      if (!BluetoothThermalPrinter.isConnected()) {
        _showErrorSnackBar(
            'الطابعة الحرارية غير متصلة. يرجى الاتصال بالطابعة من الإعدادات.');
        return;
      }

      await BluetoothThermalPrinter.printCollectionReceipt(
        invoiceReceipt: _lastSuccessfulInvoiceReceipt!
        //subscriber: widget.subscriber,
        //collectionResponse: _lastSuccessfulCollection!,
        //amountPaid: _lastAmountPaid!,
        //paymentMethod: _lastPaymentMethod!,
        //notes: _lastNotes,
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم طباعة السند على الطابعة الحرارية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في الطباعة الحرارية: ${e.toString()}');
      }
    }
  }

  Future<void> _printReceipt() async {
    if (_lastSuccessfulCollection == null ||
        _lastAmountPaid == null ||
        _lastPaymentMethod == null) {
      _showErrorSnackBar('لا توجد بيانات تحصيل للطباعة');
      return;
    }

    try {
      print(widget.subscriber.subscriberName);
      await PrintService.printCollectionReceipt(
        invoiceReceipt: _lastSuccessfulInvoiceReceipt!,
       // subscriber: widget.subscriber,
       // collectionResponse: _lastSuccessfulCollection!,
       // amountPaid: _lastAmountPaid!,
       // paymentMethod: _lastPaymentMethod!,
       // notes: _lastNotes,
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال السند للطباعة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في الطباعة: ${e.toString()}');
      }
    }
  }

  // طباعة PDF للموبايل
  Future<void> _printReceiptPDF() async {
    if (_lastSuccessfulInvoiceReceipt == null) {
      _showErrorSnackBar('لا توجد بيانات تحصيل للطباعة');
      return;
    }

    try {
      // التحقق من اتصال الطابعة الحرارية
      if (!BluetoothThermalPrinter.isConnected()) {
        _showErrorSnackBar(
            'الطابعة الحرارية غير متصلة. يرجى الاتصال بالطابعة من الإعدادات.');
        return;
      }

      // طباعة PDF على الطابعة الحرارية
      final success = await BluetoothThermalPrinter.printCollectionReceiptPDF(
        _lastSuccessfulInvoiceReceipt!,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال السند PDF للطباعة'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          _showErrorSnackBar('فشل في طباعة السند PDF');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في طباعة PDF: ${e.toString()}');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تحصيل الفواتير'),
        backgroundColor: AppTheme.themeData.primaryColor, //Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المشترك
              _buildSubscriberInfo(),
              const SizedBox(height: 20),

              // معلومات الرصيد
              _buildBalanceInfo(),
              const SizedBox(height: 20),

              // نموذج التحصيل
              _buildCollectionForm(),
              const SizedBox(height: 30),

              // زر التحصيل
              if (!_isInquiring) _buildCollectionButton(),

              // أزرار الطباعة (تظهر بعد التحصيل الناجح)
              if (_lastSuccessfulInvoiceReceipt != null) ...[
                const SizedBox(height: 20),
                _buildPrintButtons(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubscriberInfo() {
    print(widget.subscriber.subscriberName);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المشترك',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('رقم الاشتراك:', widget.subscriber.subscriberNumber),
            _buildInfoRow('اسم المشترك:', widget.subscriber.subscriberName),
            _buildInfoRow('العنوان:', widget.subscriber.address ?? 'غير محدد'),
            if (widget.subscriber.phone != null)
              _buildInfoRow('الهاتف:', widget.subscriber.phone!),
            if (widget.subscriber.meterNumber != null)
              _buildInfoRow('رقم العداد:', widget.subscriber.meterNumber!),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'معلومات الرصيد',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: _isInquiring ? null : _inquireBalance,
                  icon: _isInquiring
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.refresh),
                  tooltip: 'تحديث الرصيد',
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_balanceInfo != null && _balanceInfo!.success) ...[
              _buildInfoRow(
                'المبلغ المستحق : ',
                '${_balanceInfo!.currentBalance.toStringAsFixed(2)} ريال',
                valueColor: _balanceInfo!.currentBalance > 0
                    ? Colors.red
                    : Colors.green,
              ),
              /* _buildInfoRow(
                'آخر قراءة:', 
                _balanceInfo!.lastReading.toStringAsFixed(2),
              ),*/
              //if (_balanceInfo!.lastReadingDate != null)
              /* _buildInfoRow(
                  'تاريخ آخر قراءة:', 
                  _balanceInfo!.lastReadingDate!.toString().split(' ')[0],
                ),*/
              _buildInfoRow(
                'الفاتورة الحالية : ',
                '${_balanceInfo!.estimatedBill.toStringAsFixed(2)} ريال',
                valueColor: Colors.orange,
              ),

              _buildInfoRow(
                'تفاصيل الفاتورة : ',
                '${_balanceInfo!.message}',
                valueColor: Colors.black,
              ),
            ] else if (_balanceInfo != null && !_balanceInfo!.success) ...[
              Text(
                _balanceInfo!.message,
                style: const TextStyle(color: Colors.red),
              ),
            ] else ...[
              const Text('جاري تحميل معلومات الرصيد...'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCollectionForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل التحصيل',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // مبلغ التحصيل
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'المبلغ المستلم *',
                suffixText: 'ريال',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال المبلغ';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'يرجى إدخال مبلغ صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // طريقة الدفع
            DropdownButtonFormField<String>(
              value: _paymentMethod,
              decoration: const InputDecoration(
                labelText: 'طريقة الدفع',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'Cash', child: Text('نقدي')),
                // DropdownMenuItem(value: 'Card', child: Text('بطاقة')),
                // DropdownMenuItem(value: 'Transfer', child: Text('تحويل')),
              ],
              onChanged: (value) {
                setState(() {
                  _paymentMethod = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // ملاحظات
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCollectionButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _processCollection,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : const Text(
                'إرسال عملية التحصيل',
                style: TextStyle(fontSize: 16),
              ),
      ),
    );
  }

  Widget _buildPrintButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'طباعة الإيصال',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // زر الطباعة العادية (النصية)
            SizedBox(
              width: double.infinity,
              height: 45,
              child: ElevatedButton.icon(
                onPressed: _printReceipt,
                icon: const Icon(Icons.print),
                label: const Text('طباعة عادية (نص)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),

            const SizedBox(height: 12),

            // زر طباعة PDF
            SizedBox(
              width: double.infinity,
              height: 45,
              child: ElevatedButton.icon(
                onPressed: _printReceiptPDF,
                icon: const Icon(Icons.picture_as_pdf),
                label: const Text('طباعة PDF (منسقة)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),

            const SizedBox(height: 8),

            const Text(
              'ملاحظة: طباعة PDF تعطي تنسيق أفضل مشابه للكمبيوتر',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: valueColor),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
