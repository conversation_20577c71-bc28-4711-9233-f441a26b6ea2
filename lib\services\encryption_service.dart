import 'dart:convert';
import 'package:encrypt/encrypt.dart';

class EncryptionService {
  static const String _key = "MySecretKey12345MySecretKey12345"; // 32 bytes
  static const String _iv = "MySecretIV123456"; // 16 bytes

  static String encrypt(String plainText) {
    if (plainText.isEmpty) return '';

    try {
      // تحويل النص إلى bytes
      final plainBytes = utf8.encode(plainText);
      
      // تطبيق XOR بسيط للتشفير (يمكن تحسينه لاحقاً)
      final keyBytes = utf8.encode(_key);
      final ivBytes = utf8.encode(_iv);
      
      final encryptedBytes = <int>[];
      for (int i = 0; i < plainBytes.length; i++) {
        final keyIndex = i % keyBytes.length;
        final ivIndex = i % ivBytes.length;
        encryptedBytes.add(plainBytes[i] ^ keyBytes[keyIndex] ^ ivBytes[ivIndex]);
      }
      
      return base64.encode(encryptedBytes);
    } catch (e) {
      return '';
    }
  }

  static String decrypt(String cipherText) {
    if (cipherText.isEmpty) return '';

    try {
      final encryptedBytes = base64.decode(cipherText);
      final keyBytes = utf8.encode(_key);
      final ivBytes = utf8.encode(_iv);
      
      final decryptedBytes = <int>[];
      for (int i = 0; i < encryptedBytes.length; i++) {
        final keyIndex = i % keyBytes.length;
        final ivIndex = i % ivBytes.length;
        decryptedBytes.add(encryptedBytes[i] ^ keyBytes[keyIndex] ^ ivBytes[ivIndex]);
      }
      
      return utf8.decode(decryptedBytes);
    } catch (e) {
      return '';
    }
  }

  static String encryptObject<T>(T object) {
    final json = jsonEncode(object);
    return  encrypt(json);
  }


 /* static String decryptObject<T>(T object) {
    final json =  decrypt(object.toString())  ;
    return jsonEncode(json);
  }*/

 
  static String encryptObjectLocal<T>(T object) {
    final json = jsonEncode(object);
    return encrypt(json);
  }

  static T? decryptObjectLocal<T>(String encryptedData, T Function(Map<String, dynamic>) fromJson) {
    try {
      final decryptedJson = decrypt(encryptedData);
      if (decryptedJson.isEmpty) return null;
      
      final jsonMap = jsonDecode(decryptedJson) as Map<String, dynamic>;
      return fromJson(jsonMap);
    } catch (e) {
      return null;
    }
  }


  static T? decryptObject<T>(String encryptedData, T Function(Map<String, dynamic>) fromJson) {
    try {
      final decryptedJson = decrypt(encryptedData);
      if (decryptedJson.isEmpty) return null;
      
      final jsonMap = jsonDecode(decryptedJson) as Map<String, dynamic>;
      return fromJson(jsonMap);
    } catch (e) {
      return null;
    }
  }


  static Map<String, dynamic>? decryptToMapLocal(String encryptedData) {
    try {
      final decryptedJson = decrypt(encryptedData);
      if (decryptedJson.isEmpty) return null;
      
      return jsonDecode(decryptedJson) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  static Map<String, dynamic>? decryptToMap(String encryptedData) {
    try {
      final decryptedJson = decrypt(encryptedData);
      if (decryptedJson.isEmpty) return null;
      
      return jsonDecode(decryptedJson) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  static List<T>? decryptToList<T>(String encryptedData, T Function(Map<String, dynamic>) fromJson) {
    try {
     // print(encryptedData);
      final decryptedJson = decrypt(encryptedData);
    //  print( "decryptedJson" +  decryptedJson);
      if (decryptedJson.isEmpty) return null;
      
      final jsonList = jsonDecode(decryptedJson) as List;
      return jsonList.map((item) => fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
     // print(e.toString());
      return null;
    }
  }


    static List<T>? decryptToListLocal<T>(String encryptedData, T Function(Map<String, dynamic>) fromJson) {
    try {
     // print(encryptedData);
      final decryptedJson = decrypt(encryptedData);
    //  print( "decryptedJson" +  decryptedJson);
      if (decryptedJson.isEmpty) return null;
      
      final jsonList = jsonDecode(decryptedJson) as List;
      return jsonList.map((item) => fromJson(item as Map<String, dynamic>)).toList();
    } catch (e) {
     // print(e.toString());
      return null;
    }
  }

}
