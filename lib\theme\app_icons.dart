import 'package:flutter/material.dart';

/// نظام الأيقونات الشامل للتطبيق
/// يمكن تغيير جميع الأيقونات من هنا لتطبق على التطبيق كاملاً
class AppIcons {
  // ========== أيقونات التنقل الرئيسية ==========
  
  /// أيقونة الصفحة الرئيسية
  static const IconData home = Icons.home;
  
  /// أيقونة المشتركين
  static const IconData subscribers = Icons.people;
  
  /// أيقونة التحصيلات
  static const IconData collections = Icons.payments;
  
  /// أيقونة قراءة العدادات
  static const IconData meterReading = Icons.qr_code_scanner;
  
  /// أيقونة التقارير
  static const IconData reports = Icons.assessment;
  
  /// أيقونة الإعدادات
  static const IconData settings = Icons.settings;

  // ========== أيقونات العمليات ==========
  
  /// أيقونة البحث
  static const IconData search = Icons.search;
  
  /// أيقونة التصفية
  static const IconData filter = Icons.filter_list;
  
  /// أيقونة الإضافة
  static const IconData add = Icons.add;
  
  /// أيقونة التعديل
  static const IconData edit = Icons.edit;
  
  /// أيقونة الحذف
  static const IconData delete = Icons.delete;
  
  /// أيقونة الحفظ
  static const IconData save = Icons.save;
  
  /// أيقونة الإلغاء
  static const IconData cancel = Icons.cancel;
  
  /// أيقونة التأكيد
  static const IconData confirm = Icons.check_circle;

  // ========== أيقونات المعلومات ==========
  
  /// أيقونة المعلومات
  static const IconData info = Icons.info;
  
  /// أيقونة التحذير
  static const IconData warning = Icons.warning;
  
  /// أيقونة الخطأ
  static const IconData error = Icons.error;
  
  /// أيقونة النجاح
  static const IconData success = Icons.check_circle;

  // ========== أيقونات التحصيلات ==========
  
  /// أيقونة الدفع
  static const IconData payment = Icons.payment;
  
  /// أيقونة المبلغ
  static const IconData amount = Icons.attach_money;
  
  /// أيقونة الرصيد
  static const IconData balance = Icons.account_balance_wallet;
  
  /// أيقونة الفاتورة
  static const IconData invoice = Icons.receipt;
  
  /// أيقونة الطباعة
  static const IconData print = Icons.print;

  // ========== أيقونات قراءة العدادات ==========
  
  /// أيقونة مسح QR
  static const IconData qrScan = Icons.qr_code_scanner;
  
  /// أيقونة الكاميرا
  static const IconData camera = Icons.camera_alt;
  
  /// أيقونة المعرض
  static const IconData gallery = Icons.photo_library;
  
  /// أيقونة العداد
  static const IconData meter = Icons.speed;
  
  /// أيقونة القراءة
  static const IconData reading = Icons.visibility;

  // ========== أيقونات المشتركين ==========
  
  /// أيقونة المشترك
  static const IconData subscriber = Icons.person;
  
  /// أيقونة المشتركين
  static const IconData subscribersList = Icons.people;
  
  /// أيقونة العنوان
  static const IconData address = Icons.location_on;
  
  /// أيقونة الهاتف
  static const IconData phone = Icons.phone;
  
  /// أيقونة البريد الإلكتروني
  static const IconData email = Icons.email;

  // ========== أيقونات التقارير ==========
  
  /// أيقونة التقرير
  static const IconData report = Icons.description;
  
  /// أيقونة الإحصائيات
  static const IconData statistics = Icons.bar_chart;
  
  /// أيقونة التصدير
  static const IconData export = Icons.file_download;
  
  /// أيقونة التاريخ
  static const IconData date = Icons.calendar_today;
  
  /// أيقونة الفترة
  static const IconData dateRange = Icons.date_range;

  // ========== أيقونات النظام ==========
  
  /// أيقونة تسجيل الدخول
  static const IconData login = Icons.login;
  
  /// أيقونة تسجيل الخروج
  static const IconData logout = Icons.logout;
  
  /// أيقونة المستخدم
  static const IconData user = Icons.account_circle;
  
  /// أيقونة المزامنة
  static const IconData sync = Icons.sync;
  
  /// أيقونة التحديث
  static const IconData refresh = Icons.refresh;
  
  /// أيقونة التحميل
  static const IconData download = Icons.download;
  
  /// أيقونة الرفع
  static const IconData upload = Icons.upload;

  // ========== أيقونات الحالات ==========
  
  /// أيقونة نشط
  static const IconData active = Icons.check_circle;
  
  /// أيقونة غير نشط
  static const IconData inactive = Icons.cancel;
  
  /// أيقونة قيد الانتظار
  static const IconData pending = Icons.hourglass_empty;
  
  /// أيقونة مكتمل
  static const IconData completed = Icons.done_all;

  // ========== أيقونات التنقل ==========
  
  /// أيقونة الرجوع
  static const IconData back = Icons.arrow_back;
  
  /// أيقونة التالي
  static const IconData next = Icons.arrow_forward;
  
  /// أيقونة الأعلى
  static const IconData up = Icons.keyboard_arrow_up;
  
  /// أيقونة الأسفل
  static const IconData down = Icons.keyboard_arrow_down;
  
  /// أيقونة اليسار
  static const IconData left = Icons.keyboard_arrow_left;
  
  /// أيقونة اليمين
  static const IconData right = Icons.keyboard_arrow_right;

  // ========== أيقونات الملفات ==========
  
  /// أيقونة الملف
  static const IconData file = Icons.insert_drive_file;
  
  /// أيقونة الصورة
  static const IconData image = Icons.image;
  
  /// أيقونة PDF
  static const IconData pdf = Icons.picture_as_pdf;
  
  /// أيقونة Excel
  static const IconData excel = Icons.table_chart;
  
  /// أيقونة المجلد
  static const IconData folder = Icons.folder;

  // ========== دوال مساعدة ==========
  
  /// الحصول على أيقونة ملونة
  static Icon getColoredIcon(IconData icon, Color color, {double? size}) {
    return Icon(icon, color: color, size: size);
  }
  
  /// الحصول على أيقونة بحجم محدد
  static Icon getSizedIcon(IconData icon, double size, {Color? color}) {
    return Icon(icon, size: size, color: color);
  }
  
  /// الحصول على أيقونة للأزرار
  static Icon getButtonIcon(IconData icon, {Color? color}) {
    return Icon(icon, size: 20, color: color);
  }
  
  /// الحصول على أيقونة للعناوين
  static Icon getTitleIcon(IconData icon, {Color? color}) {
    return Icon(icon, size: 24, color: color);
  }
  
  /// الحصول على أيقونة كبيرة
  static Icon getLargeIcon(IconData icon, {Color? color}) {
    return Icon(icon, size: 48, color: color);
  }
}

/// مجموعات أيقونات بديلة
class AppIconThemes {
  /// نمط الأيقونات الحديث (Material Icons)
  static const Map<String, IconData> modernTheme = {
    'home': Icons.home_outlined,
    'subscribers': Icons.people_outline,
    'collections': Icons.payments_outlined,
    'meterReading': Icons.qr_code_scanner_outlined,
    'reports': Icons.assessment_outlined,
    'settings': Icons.settings_outlined,
  };
  
  /// نمط الأيقونات المملوءة
  static const Map<String, IconData> filledTheme = {
    'home': Icons.home,
    'subscribers': Icons.people,
    'collections': Icons.payments,
    'meterReading': Icons.qr_code_scanner,
    'reports': Icons.assessment,
    'settings': Icons.settings,
  };
  
  /// نمط الأيقونات الحادة
  static const Map<String, IconData> sharpTheme = {
    'home': Icons.home_sharp,
    'subscribers': Icons.people_sharp,
    'collections': Icons.payments_sharp,
    'meterReading': Icons.qr_code_scanner_sharp,
    'reports': Icons.assessment_sharp,
    'settings': Icons.settings_sharp,
  };
}
