/*class CollectionReport {
  final String transactionId; //id
  final String subscriberNumber;  // partner_id[0]
  final String subscriberName;  // partner_id[1]
  final String? address;      //null
  final String? meterNumber;   // null
  final double amountDue;     //x_amount_total
  final double amountPaid;  // amount_total
  final String paymentMethod; // cash
  final double newBalance;    // null
  final String transactionDate;  // date_invoice
  final String? notes;            // null
  final String collectorName ;    // user_id [1]

  CollectionReport({
    required this.transactionId,
    required this.subscriberNumber,
    required this.subscriberName,
    this.address,
    this.meterNumber,
    required this.amountDue,
    required this.amountPaid,
    required this.paymentMethod,
    required this.newBalance,
    required this.transactionDate,
    this.notes,
    required this.collectorName,
  });

  factory CollectionReport.fromJson(Map<String, dynamic> json) {
    return CollectionReport(
      transactionId: json['transactionId']?.toString() ?? '',
      subscriberNumber: json['subscriberNumber']?.toString() ?? '',
      subscriberName: json['subscriberName']?.toString() ?? '',
      address: json['address']?.toString(),
      meterNumber: json['meterNumber']?.toString(),
      amountDue: (json['amountDue'] as num?)?.toDouble() ?? 0.0,
      amountPaid: (json['amountPaid'] as num?)?.toDouble() ?? 0.0,
      paymentMethod: json['paymentMethod']?.toString() ?? '',
      newBalance: (json['newBalance'] as num?)?.toDouble() ?? 0.0,
      transactionDate:   json['transactionDate']?.toString() ?? ''   ,
      notes: json['notes']?.toString(),
      collectorName: json['collectorName']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transactionId': transactionId,
      'subscriberNumber': subscriberNumber,
      'subscriberName': subscriberName,
      'address': address,
      'meterNumber': meterNumber,
      'amountDue': amountDue,
      'amountPaid': amountPaid,
      'paymentMethod': paymentMethod,
      'newBalance': newBalance,
      'transactionDate': transactionDate,
      'notes': notes,
      'collectorName': collectorName,
    };
  }

  // Helper method to get payment method in Arabic
  String get paymentMethodArabic {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return 'نقدي';
      case 'card':
        return 'بطاقة';
      case 'transfer':
        return 'تحويل';
      default:
        return paymentMethod;
    }
  }

  // Helper method to format date
  String get formattedDate {
    return '${transactionDate.year}/${transactionDate.month.toString().padLeft(2, '0')}/${transactionDate.day.toString().padLeft(2, '0')}';
  }

  // Helper method to format time
  String get formattedTime {
    return '${transactionDate.hour.toString().padLeft(2, '0')}:${transactionDate.minute.toString().padLeft(2, '0')}';
  }
}

*/
 class CollectionReport {
  final String transactionId;
  final String subscriberNumber;
  final String subscriberName;
  final String? address;
  final String? meterNumber;
  final double amountDue;
  final double amountPaid;
  final String paymentMethod;
  final double newBalance;
  final DateTime transactionDate;
  final String? notes;
  final String collectorName;

  CollectionReport({
    required this.transactionId,
    required this.subscriberNumber,
    required this.subscriberName,
    this.address,
    this.meterNumber,
    required this.amountDue,
    required this.amountPaid,
    required this.paymentMethod,
    required this.newBalance,
    required this.transactionDate,
    this.notes,
    required this.collectorName,
  });

  factory CollectionReport.fromJson(Map<String, dynamic> json) {
    return CollectionReport(
      transactionId: json['transactionId']?.toString() ?? '',
      subscriberNumber: json['subscriberNumber']?.toString() ?? '',
      subscriberName: json['subscriberName']?.toString() ?? '',
      address: json['address']?.toString(),
      meterNumber: json['meterNumber']?.toString(),
      amountDue: (json['amountDue'] as num?)?.toDouble() ?? 0.0,
      amountPaid: (json['amountPaid'] as num?)?.toDouble() ?? 0.0,
      paymentMethod: json['paymentMethod']?.toString() ?? '',
      newBalance: (json['newBalance'] as num?)?.toDouble() ?? 0.0,
      transactionDate: json['transactionDate'] != null 
          ? DateTime.parse(json['transactionDate'].toString())
          : DateTime.now(),
      notes: json['notes']?.toString(),
      collectorName: json['collectorName']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transactionId': transactionId,
      'subscriberNumber': subscriberNumber,
      'subscriberName': subscriberName,
      'address': address,
      'meterNumber': meterNumber,
      'amountDue': amountDue,
      'amountPaid': amountPaid,
      'paymentMethod': paymentMethod,
      'newBalance': newBalance,
      'transactionDate': transactionDate.toIso8601String(),
      'notes': notes,
      'collectorName': collectorName,
    };
  }

  // Helper method to get payment method in Arabic
  String get paymentMethodArabic {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return 'نقدي';
      case 'card':
        return 'بطاقة';
      case 'transfer':
        return 'تحويل';
      default:
        return paymentMethod;
    }
  }

  // Helper method to format date
  String get formattedDate {
    return '${transactionDate.year}/${transactionDate.month.toString().padLeft(2, '0')}/${transactionDate.day.toString().padLeft(2, '0')}';
  }

  // Helper method to format time
  String get formattedTime {
    return '${transactionDate.hour.toString().padLeft(2, '0')}:${transactionDate.minute.toString().padLeft(2, '0')}';
  }
}

class BillingsRec {
    BillingsRec({
        required this.createUid,
      //  required this.refundInvoiceId,
        required this.userId,
        required this.xAmountTotal,
      //  required this.xDateEnd,
        required this.xNut1,
        required this.dateInvoice,
      //  required this.previousInvoiceId,
        required this.xNumber,
        required this.displayName,
        required this.writeUid,
        required this.partnerId,
        required this.id,
         required this.paymentType,
      //  required this.moveId,
        required this.amountTotal,
    });

    final List<dynamic> createUid;
   // final bool? refundInvoiceId;
    final List<dynamic> userId;
    final double? xAmountTotal;
  //  final DateTime? xDateEnd;
    final List<dynamic> xNut1;
    final DateTime? dateInvoice;
  //  final dynamic? previousInvoiceId;
    final String? xNumber;
    final String? displayName;
    final List<dynamic> writeUid;
    final List<dynamic> partnerId;
    final String? id;
     final String? paymentType;
   // final List<dynamic> moveId;
    final double? amountTotal;

    factory BillingsRec.fromJson(Map<String, dynamic> json){ 
        return BillingsRec(
            createUid: json["create_uid"] == null ? [] : List<dynamic>.from(json["create_uid"]!.map((x) => x)),
           // refundInvoiceId: json["refund_invoice_id"],
           userId: json["write_uid"] == null ? [] : List<dynamic>.from(json["write_uid"]!.map((x) => x)),
            xAmountTotal: double.parse(json["x_invoice_amount"].toString())   ,
           // xDateEnd: DateTime.tryParse(json["x_date_end"] ?? ""),
            xNut1: json["x_nut1"] == null ? [] : List<dynamic>.from(json["x_nut1"]!.map((x) => x)),
         dateInvoice: DateTime.tryParse(json["payment_date"] ?? ""),
           // previousInvoiceId: json["previous_invoice_id"],
            xNumber: json["id"].toString(),
            displayName: json["display_name"],
            writeUid: json["write_uid"] == null ? [] : List<dynamic>.from(json["write_uid"]!.map((x) => x)),
            partnerId: json["partner_id"] == null ? [] : List<dynamic>.from(json["partner_id"]!.map((x) => x)),
            id: json["display_name"]  ,//json["id"].toString(),
              paymentType: json["payment_type"].toString(),
            //moveId: json["move_id"] == null ? [] : List<dynamic>.from(json["move_id"]!.map((x) => x)),
            amountTotal:  double.parse(json["amount"].toString()) ,
        );
    }

    

   /* @override
    String toString(){
        return "$createUid, $refundInvoiceId, $userId, $xAmountTotal, $xDateEnd, $xNut1, $dateInvoice, $previousInvoiceId, $xNumber, $displayName, $writeUid, $partnerId, $id, $moveId, $amountTotal, ";
    }*/
}


/*class BillingsRec {
  List<int>? createUid;
  bool? refundInvoiceId;
  List<String>? userId;
  double? xAmountTotal;
  String? xDateEnd;
  List<int>? xNut1;
  String? dateInvoice;
  List<int>? previousInvoiceId;
  int? xNumber;
  String? displayName;
  List<int>? writeUid;
  List<String>? partnerId;
  String? id;
  List<int>? moveId;
  int? amountTotal;

  BillingsRec(
      {this.createUid,
      this.refundInvoiceId,
      this.userId,
      this.xAmountTotal,
      this.xDateEnd,
      this.xNut1,
      this.dateInvoice,
      this.previousInvoiceId,
      this.xNumber,
      this.displayName,
      this.writeUid,
      this.partnerId,
      this.id,
      this.moveId,
      this.amountTotal});

   factory BillingsRec.fromJson(Map<String, dynamic> json) {
     return BillingsRec(

    createUid : json['create_uid'].cast<int>(),
    refundInvoiceId : json['refund_invoice_id'],
    userId : json['user_id'].cast<int>(),
    xAmountTotal : json['x_amount_total'],
    xDateEnd : json['x_date_end'],
    xNut1 : json['x_nut1'].cast<int>(),
    dateInvoice : json['date_invoice'],
    previousInvoiceId : json['previous_invoice_id'].cast<int>(),
    xNumber : json['x_number'],
    displayName : json['display_name'],
    writeUid : json['write_uid'].cast<int>(),
    partnerId : json['partner_id'].cast<int>(),
    id : json['id'],
    moveId : json['move_id'].cast<int>(),
    amountTotal : json['amount_total'],
     );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['create_uid'] = this.createUid;
    data['refund_invoice_id'] = this.refundInvoiceId;
    data['user_id'] = this.userId;
    data['x_amount_total'] = this.xAmountTotal;
    data['x_date_end'] = this.xDateEnd;
    data['x_nut1'] = this.xNut1;
    data['date_invoice'] = this.dateInvoice;
    data['previous_invoice_id'] = this.previousInvoiceId;
    data['x_number'] = this.xNumber;
    data['display_name'] = this.displayName;
    data['write_uid'] = this.writeUid;
    data['partner_id'] = this.partnerId;
    data['id'] = this.id;
    data['move_id'] = this.moveId;
    data['amount_total'] = this.amountTotal;
    return data;
  }
}
*/
class CollectionReportRequest {
  final int? userid;
    final String Billing_No;
  final String Date_From;
  final String Date_To;
 

  CollectionReportRequest({
     this.userid,
      required this.Billing_No,
    required this.Date_From,
    required this.Date_To,
 
  });

  Map<String, dynamic> toJson() {
    return {
      'userid': userid,
        'Billing_No': Billing_No,
      'Date_From': Date_From,
      'Date_To': Date_To,
 
    };
  }
}


class CollectionReportResponse {
  final bool success;
  final String message;
  final List<BillingsRec> collections;
  final CollectionReportSummary? summary;

  CollectionReportResponse({
    required this.success,
    required this.message,
    required this.collections,
    this.summary,
  });

  factory CollectionReportResponse.fromJson(Map<String, dynamic> json) {
    return CollectionReportResponse(
      success: json['success'] ?? false,
      message: json['message']?.toString() ?? '',
      collections: (json['collections'] as List<dynamic>?)
          ?.map((item) => BillingsRec.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      summary: json['summary'] != null 
          ? CollectionReportSummary.fromJson(json['summary'] as Map<String, dynamic>)
          : null,
    );
  }
}

class CollectionReportSummary {
  final int totalTransactions;
  final double totalAmountDue;
  final double totalAmountPaid;
  final double totalOutstanding;
  final Map<String, int> paymentMethodCounts;
  final Map<String, double> paymentMethodAmounts;

  CollectionReportSummary({
    required this.totalTransactions,
    required this.totalAmountDue,
    required this.totalAmountPaid,
    required this.totalOutstanding,
    required this.paymentMethodCounts,
    required this.paymentMethodAmounts,
  });

  factory CollectionReportSummary.fromJson(Map<String, dynamic> json) {
    return CollectionReportSummary(
      totalTransactions: json['totalTransactions'] ?? 0,
      totalAmountDue: (json['totalAmountDue'] as num?)?.toDouble() ?? 0.0,
      totalAmountPaid: (json['totalAmountPaid'] as num?)?.toDouble() ?? 0.0,
      totalOutstanding: (json['totalOutstanding'] as num?)?.toDouble() ?? 0.0,
      paymentMethodCounts: Map<String, int>.from(json['paymentMethodCounts'] ?? {}),
      paymentMethodAmounts: Map<String, double>.from(
        (json['paymentMethodAmounts'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value as num).toDouble())
        ) ?? {}
      ),
    );
  }
}
