class CollectionReport {
  final String transactionId;
  final String subscriberNumber;
  final String subscriberName;
  final String? address;
  final String? meterNumber;
  final double amountDue;
  final double amountPaid;
  final String paymentMethod;
  final double newBalance;
  final DateTime transactionDate;
  final String? notes;
  final String collectorName;

  CollectionReport({
    required this.transactionId,
    required this.subscriberNumber,
    required this.subscriberName,
    this.address,
    this.meterNumber,
    required this.amountDue,
    required this.amountPaid,
    required this.paymentMethod,
    required this.newBalance,
    required this.transactionDate,
    this.notes,
    required this.collectorName,
  });

  factory CollectionReport.fromJson(Map<String, dynamic> json) {
    return CollectionReport(
      transactionId: json['transactionId']?.toString() ?? '',
      subscriberNumber: json['subscriberNumber']?.toString() ?? '',
      subscriberName: json['subscriberName']?.toString() ?? '',
      address: json['address']?.toString(),
      meterNumber: json['meterNumber']?.toString(),
      amountDue: (json['amountDue'] as num?)?.toDouble() ?? 0.0,
      amountPaid: (json['amountPaid'] as num?)?.toDouble() ?? 0.0,
      paymentMethod: json['paymentMethod']?.toString() ?? '',
      newBalance: (json['newBalance'] as num?)?.toDouble() ?? 0.0,
      transactionDate: json['transactionDate'] != null 
          ? DateTime.parse(json['transactionDate'].toString())
          : DateTime.now(),
      notes: json['notes']?.toString(),
      collectorName: json['collectorName']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'transactionId': transactionId,
      'subscriberNumber': subscriberNumber,
      'subscriberName': subscriberName,
      'address': address,
      'meterNumber': meterNumber,
      'amountDue': amountDue,
      'amountPaid': amountPaid,
      'paymentMethod': paymentMethod,
      'newBalance': newBalance,
      'transactionDate': transactionDate.toIso8601String(),
      'notes': notes,
      'collectorName': collectorName,
    };
  }

  // Helper method to get payment method in Arabic
  String get paymentMethodArabic {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return 'نقدي';
      case 'card':
        return 'بطاقة';
      case 'transfer':
        return 'تحويل';
      default:
        return paymentMethod;
    }
  }

  // Helper method to format date
  String get formattedDate {
    return '${transactionDate.year}/${transactionDate.month.toString().padLeft(2, '0')}/${transactionDate.day.toString().padLeft(2, '0')}';
  }

  // Helper method to format time
  String get formattedTime {
    return '${transactionDate.hour.toString().padLeft(2, '0')}:${transactionDate.minute.toString().padLeft(2, '0')}';
  }
}

class CollectionReportResponse {
  final bool success;
  final String message;
  final List<CollectionReport> collections;
  final CollectionReportSummary? summary;

  CollectionReportResponse({
    required this.success,
    required this.message,
    required this.collections,
    this.summary,
  });

  factory CollectionReportResponse.fromJson(Map<String, dynamic> json) {
    return CollectionReportResponse(
      success: json['success'] ?? false,
      message: json['message']?.toString() ?? '',
      collections: (json['collections'] as List<dynamic>?)
          ?.map((item) => CollectionReport.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      summary: json['summary'] != null 
          ? CollectionReportSummary.fromJson(json['summary'] as Map<String, dynamic>)
          : null,
    );
  }
}

class CollectionReportSummary {
  final int totalTransactions;
  final double totalAmountDue;
  final double totalAmountPaid;
  final double totalOutstanding;
  final Map<String, int> paymentMethodCounts;
  final Map<String, double> paymentMethodAmounts;

  CollectionReportSummary({
    required this.totalTransactions,
    required this.totalAmountDue,
    required this.totalAmountPaid,
    required this.totalOutstanding,
    required this.paymentMethodCounts,
    required this.paymentMethodAmounts,
  });

  factory CollectionReportSummary.fromJson(Map<String, dynamic> json) {
    return CollectionReportSummary(
      totalTransactions: json['totalTransactions'] ?? 0,
      totalAmountDue: (json['totalAmountDue'] as num?)?.toDouble() ?? 0.0,
      totalAmountPaid: (json['totalAmountPaid'] as num?)?.toDouble() ?? 0.0,
      totalOutstanding: (json['totalOutstanding'] as num?)?.toDouble() ?? 0.0,
      paymentMethodCounts: Map<String, int>.from(json['paymentMethodCounts'] ?? {}),
      paymentMethodAmounts: Map<String, double>.from(
        (json['paymentMethodAmounts'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value as num).toDouble())
        ) ?? {}
      ),
    );
  }
}
