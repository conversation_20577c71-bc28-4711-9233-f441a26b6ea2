import 'package:flutter/material.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/tap_bounce_container.dart';

class CustomGreenSnackBar {
  CustomGreenSnackBar(BuildContext context, Widget content,
      {SnackBarAction? snackBarAction, Color backgroundColor = Colors.green}) {
    final SnackBar snackBar = SnackBar(
        action: snackBarAction,
        backgroundColor: backgroundColor,
        content: content,
        behavior: SnackBarBehavior.floating);

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}

class CustomRedSnackBar {
  CustomRedSnackBar(BuildContext context, Widget content,
      {SnackBarAction? snackBarAction, Color backgroundColor = Colors.red}) {
    final SnackBar snackBar = SnackBar(
        action: snackBarAction,
        backgroundColor: backgroundColor,
        content: content,
        behavior: SnackBarBehavior.floating);

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}

class CustomTopSuccessSnackBar {
  CustomTopSuccessSnackBar(BuildContext context, String content) {
    showTopSnackBar(
     Overlay.of(context),
      CustomSnackBar.success(
        message: content,
      ),
    );
  }
}

class CustomTopInfoSnackBar {
  CustomTopInfoSnackBar(BuildContext context, String content) {
    showTopSnackBar(
       Overlay.of(context),
      CustomSnackBar.info(
        message: content,
      ),
    );
  }
}

class CustomTopErrorSnackBar {
  CustomTopErrorSnackBar(BuildContext context, String content) {
    showTopSnackBar(
      Overlay.of(context),
      CustomSnackBar.error(
        message: content,
      ),
    );
  }
}
