import 'package:flutter/material.dart';

/// نظام الألوان الشامل للتطبيق
/// يمكن تغيير جميع الألوان من هنا لتطبق على التطبيق كاملاً
class AppColors {
  // ========== الألوان الأساسية ==========
  
  /// اللون الأساسي للتطبيق (الأزرق الحالي)
  /// غير هذا اللون لتغيير اللون الأساسي في كل التطبيق
  static const Color primary = Color(0xFF1E3A8A);
  
  /// اللون الأساسي الفاتح
  static const Color primaryLight = Color(0xFF3B82F6);
  
  /// اللون الأساسي الداكن
  static const Color primaryDark = Color(0xFF1E40AF);
  
  /// اللون الثانوي
  static const Color secondary = Color(0xFF059669);
  
  /// اللون الثانوي الفاتح
  static const Color secondaryLight = Color(0xFF10B981);
  
  /// اللون الثانوي الداكن
  static const Color secondaryDark = Color(0xFF047857);

  // ========== ألوان الحالات ==========
  
  /// لون النجاح (أخضر)
  static const Color success = Color(0xFF10B981);
  
  /// لون التحذير (برتقالي)
  static const Color warning = Color(0xFFF59E0B);
  
  /// لون الخطر (أحمر)
  static const Color error = Color(0xFFEF4444);
  
  /// لون المعلومات (أزرق فاتح)
  static const Color info = Color(0xFF3B82F6);

  // ========== ألوان الخلفيات ==========
  
  /// خلفية التطبيق الرئيسية
  static const Color background = Color(0xFFF8FAFC);
  
  /// خلفية البطاقات
  static const Color cardBackground = Colors.white;
  
  /// خلفية الأقسام
  static const Color sectionBackground = Color(0xFFF1F5F9);

  // ========== ألوان النصوص ==========
  
  /// لون النص الأساسي
  static const Color textPrimary = Color(0xFF1F2937);
  
  /// لون النص الثانوي
  static const Color textSecondary = Color(0xFF6B7280);
  
  /// لون النص الفاتح
  static const Color textLight = Color(0xFF9CA3AF);
  
  /// لون النص على الخلفية الداكنة
  static const Color textOnDark = Colors.white;

  // ========== ألوان الحدود ==========
  
  /// لون الحدود الأساسي
  static const Color border = Color(0xFFE5E7EB);
  
  /// لون الحدود عند التركيز
  static const Color borderFocused = primary;
  
  /// لون الحدود عند الخطأ
  static const Color borderError = error;

  // ========== ألوان خاصة بالتطبيق ==========
  
  /// لون المبلغ المدفوع
  static const Color amountPaid = success;
  
  /// لون المبلغ المستحق
  static const Color amountDue = warning;
  
  /// لون المبلغ المتبقي
  static const Color amountOutstanding = error;
  
  /// لون حالة نشط
  static const Color statusActive = success;
  
  /// لون حالة غير نشط
  static const Color statusInactive = Color(0xFF6B7280);

  // ========== ألوان الظلال ==========
  
  /// ظل خفيف
  static const Color shadowLight = Color(0x0F000000);
  
  /// ظل متوسط
  static const Color shadowMedium = Color(0x1A000000);
  
  /// ظل قوي
  static const Color shadowStrong = Color(0x25000000);

  // ========== دوال مساعدة ==========
  
  /// الحصول على لون بشفافية
  static Color withOpacity(Color color, int opacity) {
    return color.withAlpha( opacity);
  }
  
  /// الحصول على لون فاتح من لون أساسي
  static Color lighten(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
  
  /// الحصول على لون داكن من لون أساسي
  static Color darken(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
}

/// مجموعات ألوان جاهزة يمكن التبديل بينها
class AppColorSchemes {
  /// النمط الأزرق (الافتراضي)
  static const Map<String, Color> blueScheme = {
    'primary': Color(0xFF1E3A8A),
    'primaryLight': Color(0xFF3B82F6),
    'primaryDark': Color(0xFF1E40AF),
    'secondary': Color(0xFF059669),
  };
  
  /// النمط الأحمر
  static const Map<String, Color> redScheme = {
    'primary': Color(0xFFDC2626),
    'primaryLight': Color(0xFFEF4444),
    'primaryDark': Color(0xFFB91C1C),
    'secondary': Color(0xFF059669),
  };
  
  /// النمط الأخضر
  static const Map<String, Color> greenScheme = {
    'primary': Color(0xFF059669),
    'primaryLight': Color(0xFF10B981),
    'primaryDark': Color(0xFF047857),
    'secondary': Color(0xFF3B82F6),
  };
  
  /// النمط البرتقالي
  static const Map<String, Color> orangeScheme = {
    'primary': Color(0xFFEA580C),
    'primaryLight': Color(0xFFF97316),
    'primaryDark': Color(0xFFC2410C),
    'secondary': Color(0xFF059669),
  };
  
  /// النمط البنفسجي
  static const Map<String, Color> purpleScheme = {
    'primary': Color(0xFF7C3AED),
    'primaryLight': Color(0xFF8B5CF6),
    'primaryDark': Color(0xFF6D28D9),
    'secondary': Color(0xFF059669),
  };
}
